package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App        App        `yaml:"app"`
	Redis      Redis      `yaml:"redis"`
	Database   Database   `yaml:"database"`
	Cloudinary Cloudinary `yaml:"cloudinary"`
	Allows     Allows     `yaml:"allows"`
	Whatsapp   Whatsapp   `yaml:"whatsapp"`
	Google     Google     `yaml:"google"`
	Apple      Apple      `yaml:"apple"`
	Centrifugo Centrifugo `yaml:"centrifugo"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	BaseUrl         string `yaml:"base_url"`
	Environment     string `yaml:"environment"` // local, development, production
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	JwtAdminSecret  string `yaml:"jwt_admin_secret"`
	JwtExpire       int    `yaml:"jwt_expire"`
	ClientID        string `yaml:"client_id"`
	AdminID         string `yaml:"admin_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
	AdminEmail      string `yaml:"admin_email"`
	AdminPassword   string `yaml:"admin_password"`
}

type Google struct {
	ClientIDs   []string `yaml:"client_ids"` // Multiple client IDs for different platforms
	RedirectURL string   `yaml:"redirect_url"`
}

type Apple struct {
	ClientID    string `yaml:"client_id"`
	TeamID      string `yaml:"team_id"`
	KeyID       string `yaml:"key_id"`
	PrivateKey  string `yaml:"private_key"`
	RedirectURL string `yaml:"redirect_url"`
}

type Redis struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	Pass string `yaml:"pass"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Cloudinary struct {
	Name      string `mapstructure:"name"`
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	APIFolder string `mapstructure:"api_folder"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

type Centrifugo struct {
	URL      string `yaml:"url"`
	APIKey   string `yaml:"api_key"`
	Secret   string `yaml:"secret"`
	TokenTTL int    `yaml:"token_ttl"` // in seconds
}

func InitConfig() *Config {
	var configs Config
	file_name, _ := filepath.Abs("./config.yaml")
	yaml_file, _ := os.ReadFile(file_name)
	yaml.Unmarshal(yaml_file, &configs)
	return &configs
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}
	filename, _ := filepath.Abs("./config.yaml")
	// Sanitize the destination path using filepath.Clean
	cleanedDst := filepath.Clean(filename)
	yamlFile, _ := os.ReadFile(cleanedDst)
	err := yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error loading config.yaml ", err)
	}
	return configs
}
