package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func FromClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("client_id")
		if client_id == config.InitConfig().App.ClientID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.<PERSON>(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func FromAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Allow OPTIONS requests for CORS preflight
		if c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		admin_id := c.GetHeader("admin_id")
		configAdminID := config.InitConfig().App.AdminID
		if admin_id == configAdminID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

// TODO: dönen response'ları düzenle
func AccountType(allowed_account_type ...int) gin.HandlerFunc {
	return func(c *gin.Context) {
		account_type, exists := c.Get(state.CurrentAccountType)
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		for _, v := range allowed_account_type {
			if account_type == v {
				c.Next()
				return
			}
		}

		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_account_type", c.GetString(state.CurrentPhoneLanguage), nil)})
	}
}

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.ID)
			c.Set(state.CurrentEmail, claims.Email)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Set(state.CurrentAccountType, claims.AccountType)
			c.Set(state.CurrentOS, claims.Os)
			c.Set(state.CurrentPurchaseID, claims.PurchaseID)
			c.Set(state.CurrentPushNotifToken, claims.PushNotifToken)

			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}
