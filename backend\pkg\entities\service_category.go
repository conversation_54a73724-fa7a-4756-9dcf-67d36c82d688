package entities

import (
	"github.com/google/uuid"
	"github.com/lib/pq"
)

type ServiceCategory struct {
	Base

	ServiceNameTR         string        `json:"service_name_tr" example:"standart home cleaning"`
	ServiceNameEN         string        `json:"service_name_en" example:"standart home cleaning"`
	ImageURL              string        `json:"image_url" example:"https://example.com/image.jpg"`
	Description           string        `json:"description" example:"standart home cleaning description"`
	MinDuration           int           `json:"min_duration" example:"60"`
	ApprovedByAdmin       bool          `json:"approved_by_admin" gorm:"default:false" example:"false"`
	IsMain                bool          `json:"is_main" gorm:"default:false" example:"false"`
	MainCategoryID        uuid.UUID     `json:"main_category_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PermittedLisanceTypes pq.Int64Array `gorm:"type:integer[]" json:"permitted_lisance_types" example:"1,2,3"`
}

type ServiceCategoryResponse struct {
	ID                    string                    `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt             string                    `json:"created_at" example:"2021-01-01 00:00:00"`
	ServiceNameTR         string                    `json:"service_name_tr" example:"standart home cleaning"`
	ServiceNameEN         string                    `json:"service_name_en" example:"standart home cleaning"`
	ImageURL              string                    `json:"image_url" example:"https://example.com/image.jpg"`
	Description           string                    `json:"description" example:"standart home cleaning description"`
	MinDuration           int                       `json:"min_duration" example:"60"`
	ApprovedByAdmin       bool                      `json:"approved_by_admin" example:"false"`
	IsMain                bool                      `json:"is_main" example:"false"`
	MainCategoryID        string                    `json:"main_category_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PermittedLisanceTypes []int64                   `json:"permitted_lisance_types" example:"1,2,3"`
	SubCategories         []ServiceCategoryResponse `json:"sub_categories"`
}

func (s *ServiceCategory) Response() ServiceCategoryResponse {
	var resp ServiceCategoryResponse

	resp.ID = s.ID.String()
	resp.CreatedAt = s.CreatedAt.Format("2006-01-02 15:04:05")
	resp.ServiceNameTR = s.ServiceNameTR
	resp.ServiceNameEN = s.ServiceNameEN
	resp.ImageURL = s.ImageURL
	resp.Description = s.Description
	resp.MinDuration = s.MinDuration
	resp.IsMain = s.IsMain
	resp.PermittedLisanceTypes = s.PermittedLisanceTypes
	resp.ApprovedByAdmin = s.ApprovedByAdmin
	resp.MainCategoryID = s.MainCategoryID.String()

	return resp
}
