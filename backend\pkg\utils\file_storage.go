package utils

import (
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/config"
)

type FileStorage struct {
	basePath string
	baseURL  string
}

func NewFileStorage() (*FileStorage, error) {
	cfg := config.InitConfig()

	var basePath, baseURL string

	switch cfg.App.Environment {
	case "local":
		basePath = "./uploads"
		baseURL = "http://localhost:8000/uploads"
	case "production":
		basePath = "./uploads" // Use relative path in production too
		baseURL = "https://api.temizlikdelisi.com/uploads"
	default:
		basePath = "./uploads"
		baseURL = "http://localhost:8000/uploads"
	}

	return &FileStorage{
		basePath: basePath,
		baseURL:  baseURL,
	}, nil
}

// EnsureDirectoryExists creates directory if it doesn't exist
func (fs *FileStorage) EnsureDirectoryExists(category string) error {
	dirPath := filepath.Join(fs.basePath, category)

	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		err = os.MkdirAll(dirPath, 0755)
		if err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dirPath, err)
		}
		log.Printf("Directory created: %s", dirPath)
	}

	return nil
}

// UploadFile saves file to local storage
func (fs *FileStorage) UploadFile(category, originalFilename string, file multipart.File, fileSize int64) (string, string, error) {
	// Ensure directory exists
	if err := fs.EnsureDirectoryExists(category); err != nil {
		return "", "", err
	}

	// Generate unique filename
	fileExt := filepath.Ext(originalFilename)
	fileName := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), fileExt)

	// Full file path
	filePath := filepath.Join(fs.basePath, category, fileName)

	// Create the file
	dst, err := os.Create(filePath)
	if err != nil {
		return "", "", fmt.Errorf("failed to create file: %v", err)
	}
	defer dst.Close()

	// Copy file content
	_, err = io.Copy(dst, file)
	if err != nil {
		return "", "", fmt.Errorf("failed to save file: %v", err)
	}

	// Generate URL
	fileURL := fmt.Sprintf("%s/%s/%s", fs.baseURL, category, fileName)

	log.Printf("File uploaded successfully: %s", filePath)
	return fileURL, fileName, nil
}

// GetFileURL returns the public URL for a file
func (fs *FileStorage) GetFileURL(category, filename string) string {
	return fmt.Sprintf("%s/%s/%s", fs.baseURL, category, filename)
}

// DeleteFile removes a file from storage
func (fs *FileStorage) DeleteFile(category, filename string) error {
	filePath := filepath.Join(fs.basePath, category, filename)

	if err := os.Remove(filePath); err != nil {
		if os.IsNotExist(err) {
			log.Printf("File not found (already deleted): %s", filePath)
			return nil // Don't return error if file doesn't exist
		}
		return fmt.Errorf("failed to delete file: %v", err)
	}

	log.Printf("File deleted: %s", filePath)
	return nil
}

// UploadProfilePhoto uploads profile photo
func UploadProfilePhoto(file multipart.File, fileSize int64, contentType, originalFilename string) (string, string, error) {
	fs, err := NewFileStorage()
	if err != nil {
		return "", "", err
	}

	return fs.UploadFile("profile-photos", originalFilename, file, fileSize)
}

// UploadCriminalRecordDocument uploads criminal record document
func UploadCriminalRecordDocument(file multipart.File, fileSize int64, contentType, originalFilename string) (string, string, error) {
	fs, err := NewFileStorage()
	if err != nil {
		return "", "", err
	}

	return fs.UploadFile("criminal-records", originalFilename, file, fileSize)
}

func UploadMessageFile(file multipart.File, fileSize int64, contentType, originalFilename string) (string, string, error) {
	fs, err := NewFileStorage()
	if err != nil {
		return "", "", err
	}

	return fs.UploadFile("message-files", originalFilename, file, fileSize)
}

// InitializeDirectories creates all required directories based on environment
func InitializeDirectories() error {
	cfg := config.InitConfig()

	fs, err := NewFileStorage()
	if err != nil {
		return fmt.Errorf("failed to create file storage: %v", err)
	}

	// Define directories based on environment
	var directories []string

	switch cfg.App.Environment {
	case "local", "development":
		directories = []string{
			"profile-photos",
			"criminal-records",
			"message-files",
			"documents",
			"temp-files",
		}
		log.Printf("Initializing directories for %s environment", cfg.App.Environment)
	case "production":
		directories = []string{
			"profile-photos",
			"criminal-records",
			"message-files",
			"documents",
			"backups",
			"logs",
		}
		log.Printf("Initializing directories for production environment")
	default:
		directories = []string{
			"profile-photos",
			"criminal-records",
			"message-files",
		}
		log.Printf("Initializing default directories for environment: %s", cfg.App.Environment)
	}

	// Create all directories
	for _, dirName := range directories {
		err := fs.EnsureDirectoryExists(dirName)
		if err != nil {
			log.Printf("Failed to create directory %s: %v", dirName, err)
			continue
		}
		log.Printf("✅ Directory '%s' ready", dirName)
	}

	log.Printf("🚀 All directories initialized successfully for %s environment", cfg.App.Environment)
	return nil
}

// DeleteCriminalRecordDocument deletes criminal record document
func DeleteCriminalRecordDocument(filename string) error {
	fs, err := NewFileStorage()
	if err != nil {
		return err
	}

	return fs.DeleteFile("criminal-records", filename)
}

// DeleteProfilePhoto deletes profile photo
func DeleteProfilePhoto(filename string) error {
	fs, err := NewFileStorage()
	if err != nil {
		return err
	}

	return fs.DeleteFile("profile-photos", filename)
}

func DeleteMessageFile(filename string) error {
	fs, err := NewFileStorage()
	if err != nil {
		return err
	}

	return fs.DeleteFile("message-files", filename)
}
