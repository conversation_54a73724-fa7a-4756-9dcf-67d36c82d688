package entities

type Version struct {
	Base
	AndroidVersionName            string `json:"android_version_name"`
	AndroidBuildNumber            int    `json:"android_build_number"`
	AndroidForceUpdateBuildNumber int    `json:"android_force_update_build_number"`
	IosForceUpdateBuildNumber     int    `json:"ios_force_update_build_number"`
	IosVersionName                string `json:"ios_version_name"`
	IosBuildNumber                int    `json:"ios_build_number"`
	IsForce                       bool   `json:"is_force"`
}

type VersionResponse struct {
	AndroidVersionName            string `json:"android_version_name"`
	AndroidBuildNumber            int    `json:"android_build_number"`
	AndroidForceUpdateBuildNumber int    `json:"android_force_update_build_number"`
	IosForceUpdateBuildNumber     int    `json:"ios_force_update_build_number"`
	IosVersionName                string `json:"ios_version_name"`
	IosBuildNumber                int    `json:"ios_build_number"`
	IsForce                       bool   `json:"is_force"`
}

func (v *Version) Response() VersionResponse {
	var resp VersionResponse

	resp.AndroidVersionName = v.AndroidVersionName
	resp.AndroidBuildNumber = v.AndroidBuildNumber
	resp.AndroidForceUpdateBuildNumber = v.AndroidForceUpdateBuildNumber
	resp.IosForceUpdateBuildNumber = v.IosForceUpdateBuildNumber
	resp.IosVersionName = v.IosVersionName
	resp.IosBuildNumber = v.IosBuildNumber
	resp.IsForce = v.IsForce

	return resp
}

// VersionResponseForSwagger is the response for swagger
type VersionResponseForSwagger struct {
	Data   VersionResponse `json:"data"`
	Status int             `json:"status" example:"200"`
}
