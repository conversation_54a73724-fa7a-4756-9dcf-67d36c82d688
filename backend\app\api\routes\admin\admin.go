package admin

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/admin"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/middleware"
	"github.com/temizlik-delisi/pkg/state"
)

func AdminRoutes(r *gin.RouterGroup, s admin.Service) {
	a := r.Group("/admin")

	// Login endpoint without middleware
	a.POST("/login", AdminLogin(s))

	// Logout endpoint with middleware
	a.POST("/logout", middleware.AdminAuthorized(), AdminLogout(s))

	// Protected admin endpoints with AdminAuthorized middleware
	protected := a.Group("")
	protected.Use(middleware.AdminAuthorized())
	{
		protected.POST("/service-category/create", CreateServiceCategory(s))
		protected.POST("/service-category/delete", DeleteServiceCategory(s))
		protected.POST("/service-category/update", UpdateServiceCategory(s))
		protected.GET("/service-category/get-all", GetAllServiceCategory(s))

		protected.GET("/comment/get-all", GetAllComment(s))
		protected.POST("/comment/delete", DeleteComment(s))

		protected.POST("/blog/create", CreateBlog(s))
		protected.POST("/blog/delete", DeleteBlog(s))
		protected.POST("/blog/update", UpdateBlog(s))
		protected.GET("/blog/get-all", GetAllBlog(s))
		protected.GET("/blog/get-by-id", GetBlogByID(s))

		protected.POST("/order/get-all", GetAllOrder(s))
		protected.POST("/order/get-by-id", GetOrderByID(s))
		protected.POST("/order/delete", DeleteOrder(s))

		protected.POST("/preference/create", CreatePreference(s))
		protected.POST("/preference/update", UpdatePreference(s))
		protected.POST("/preference/delete", DeletePreference(s))
		protected.POST("/preference/get-all", GetAllPreference(s))
		protected.POST("/preference/get-by-id", GetPreferenceByID(s))

		protected.POST("/version/new", NewVersion(s))

		protected.POST("/verification/tc/approved", VerificationApprovedTC(s))

		protected.GET("/customer/get-all", GetAllCustomer(s))
		protected.GET("/customer/get-by-id/:id", GetCustomerByID(s))
		protected.GET("/cleaner/get-all", GetAllCleaner(s))
		protected.GET("/cleaner/get-by-id/:id", GetCleanerByID(s))

		// Conversation and Message endpoints
		protected.POST("/conversation/account", GetAllConversationsByAccount(s))
		protected.POST("/conversation/messages", GetMessagesByConversationID(s))

		protected.GET("/dashboard", GetDashboard(s))
		protected.GET("/test", TestAdmin(s))
	}
}

// @Summary Admin Login
// @Description Admin Login
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForLogin true "request payload for admin login"
// @Success 200 {object} dtos.AuthenticationResponse
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/login [POST]
func AdminLogin(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForLogin
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Admin Login Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.AdminLogin(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Admin Login Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "Invalid credentials",
				"status": 401,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Admin Login",
			Message:   "Admin Login Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, resp)
	}
}

// @Summary Admin Logout
// @Description Admin Logout
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/logout [POST]
func AdminLogout(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		adminID, exists := c.Get(state.CurrentAdminID)
		if !exists {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "Unauthorized",
				"status": 401,
			})
			return
		}

		err := s.AdminLogout(c, adminID.(string))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Admin Logout Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  "Internal server error",
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Admin Logout",
			Message:   "Admin Logout Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"message": "Logout successful",
			"status":  200,
		})
	}
}

// @Summary Create Service Category
// @Description Create Service Category
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateServiceCategory true "request payload for create service category"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/create [POST]
func CreateServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Service Category Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.ServiceCategoryCreate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Create Service Category",
			Message:   "Service Category Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Service Category Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Delete Service Category
// @Description Delete Service Category
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for delete service category"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/delete [POST]
func DeleteServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Service Category Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if err := s.ServiceCategoryDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Service Category",
			Message:   "Service Category Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Service Category Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Update Service Category
// @Description Update Service Category
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateServiceCategory true "request payload for update service category"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/update [POST]
func UpdateServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Service Category Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.ServiceCategoryUpdate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Update Service Category",
			Message:   "Service Category Updated Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Service Category Updated Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Service Category
// @Description Get All Service Category
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all service types"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/get-all [GET]
func GetAllServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		approved, _ := strconv.Atoi(c.Query("approved"))
		is_main, _ := strconv.ParseBool(c.Query("is_main"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.ServiceCategoryGetAll(c, page, per_page, approved, is_main)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Service Category",
			Message:   "Service Category Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get All Comment
// @Description Get All Comment
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all comments"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/comment/get-all [GET]
func GetAllComment(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		approved, _ := strconv.Atoi(c.Query("approved"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.CommentGetAll(c, page, per_page, approved)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Comment Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Comment",
			Message:   "Comment Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete Comment
// @Description Delete Comment
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for delete comment"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/comment/delete [POST]
func DeleteComment(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Comment Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if err := s.CommentDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Comment Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Comment",
			Message:   "Comment Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Comment Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Create Blog
// @Description Create Blog
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateBlog true "request payload for create blog"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/create [POST]
func CreateBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateBlog
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Blog Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.BlogCreate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Create Blog",
			Message:   "Blog Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Blog Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Delete Blog
// @Description Delete Blog
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Param payload body dtos.RequestForDeleteBlog true "request payload for delete blog"
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/delete [POST]
func DeleteBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteBlog
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Comment Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.BlogDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Blog",
			Message:   "Blog Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Blog Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Update Blog
// @Description Update Blog
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateBlog true "request payload for update blog"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/update [POST]
func UpdateBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateBlog
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Blog Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.BlogUpdate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:   "Update Blog Error",
				Message: "Error: " + err.Error(),
				Type:    "error",
				Proto:   "http",
				Ip:      state.GetCurrentUserIP(c),
				Url:     c.Request.URL.Path,
				OS:      "web",

				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Update Blog",
			Message:   "Blog Updated Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Blog Updated Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Blog
// @Description Get All Blog
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all blogs"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/get-all [GET]
func GetAllBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		approved, _ := strconv.Atoi(c.Query("approved"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.BlogGetAll(c, page, per_page, approved)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Blog",
			Message:   "Blog Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Blog By ID
// @Description Get Blog By ID
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param id path string true "blog id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/get-by-id [GET]
func GetBlogByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Blog Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.BlogGetByID(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get Blog",
			Message:   "Blog Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get All Order
// @Description Get All Order
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param payload body dtos.RequestForOrderGetAllByID true "request payload for get all order"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/order/get-all [POST]
func GetAllOrder(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForOrderGetAllByID
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Order Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if req.Page == 0 {
			req.Page = 1
		}
		if req.PerPage == 0 {
			req.PerPage = 10
		}
		resp, err := s.OrderGetAll(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Order Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Order",
			Message:   "Order Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Order By ID
// @Description Get Order By ID
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param id path string true "order id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/order/get-by-id [POST]
func GetOrderByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Order Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.OrderGetByID(c, uuid.MustParse(req.ID))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Order Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Get Order",
			Message:   "Order Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete Order
// @Description Delete Order
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for delete order"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/order/delete [POST]
func DeleteOrder(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Order Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if err := s.OrderDelete(c, uuid.MustParse(req.ID)); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Order Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Order",
			Message:   "Order Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   "Order Deleted Successfully",
			"status": 200,
		})
	}
}

// @Summary Create Preference
// @Description Create Preference
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreatePreference true "request payload for create preference"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/create [POST]
func CreatePreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreatePreference
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.PreferenceCreate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Create Preference",
			Message:   "Preference Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Preference Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Update Preference
// @Description Update Preference
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdatePreference true "request payload for update preference"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/update [POST]
func UpdatePreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdatePreference
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.PreferenceUpdate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Update Preference",
			Message:   "Preference Updated Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Preference Updated Successfully",
			"status": 201,
		})
	}
}

// @Summary Delete Preference
// @Description Delete Preference
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeletePreference true "request payload for delete preference"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/delete [POST]
func DeletePreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeletePreference
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.PreferenceDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Preference",
			Message:   "Preference Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Preference Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Preference
// @Description Get All Preference
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all preferences"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/get-all [POST]
func GetAllPreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForPaginate
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if req.Page == 0 {
			req.Page = 1
		}
		if req.PerPage == 0 {
			req.PerPage = 10
		}
		resp, err := s.PreferenceGetAll(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Preference",
			Message:   "Preference Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Preference By ID
// @Description Get Preference By ID
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param id path string true "preference id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/get-by-id [POST]
func GetPreferenceByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Preference Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.PreferenceGetByID(c, uuid.MustParse(req.ID))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Get Preference",
			Message:   "Preference Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary New Version
// @Description New Version
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForNewVersion true "request payload for new version"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/version/new [POST]
func NewVersion(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForNewVersion
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "New Version Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.NewVersion(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "New Version Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "New Version",
			Message:   "New Version Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "New Version Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Approved TC
// @Description Approved TC
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for approved tc"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/verification/tc/approved [POST]
func VerificationApprovedTC(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Approved TC Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.VerificationApprovedTC(c, uuid.MustParse(req.ID)); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Approved TC Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Approved TC",
			Message:   "TC Approved for" + req.ID + " Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "TC Approved Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Customer
// @Description Get All Customer
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/customer/get-all [GET]
func GetAllCustomer(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.CustomerGetAll(c, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Customer Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Customer",
			Message:   "Customer Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get All Cleaner
// @Description Get All Cleaner
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/cleaner/get-all [GET]
func GetAllCleaner(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.CleanerGetAll(c, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Cleaner Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Cleaner",
			Message:   "Cleaner Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Customer By ID
// @Description Get Customer By ID
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param id path string true "customer id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/customer/get-by-id/{id} [GET]
func GetCustomerByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Customer Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.CustomerGetByID(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Customer Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get Customer",
			Message:   "Customer Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Cleaner By ID
// @Description Get Cleaner By ID
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Param id path string true "cleaner id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/cleaner/get-by-id/{id} [GET]
func GetCleanerByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Cleaner Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.CleanerGetByID(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Cleaner Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get Cleaner",
			Message:   "Cleaner Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Dashboard Stats
// @Description Get Dashboard Statistics including total counts for cleaners, customers, categories, conversations, messages, orders, and offers
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} dtos.DashboardStatsResponse
// @Failure 500 {object} map[string]any
// @Router /admin/dashboard [GET]
func GetDashboard(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetDashboardStats(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Dashboard Stats Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get Dashboard Stats",
			Message:   "Dashboard Stats Retrieved Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Test Admin
// @Description Test Admin endpoint for CORS
// @Tags Admin Endpoints
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} map[string]any
// @Router /admin/test [GET]
func TestAdmin(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Admin endpoint working!",
			"status":  200,
		})
	}
}

// @Summary Get All Conversations by Account
// @Description Get all conversations for a specific account (customer or cleaner)
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForGetConversationsByAccount true "request payload for get conversations by account"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/conversation/account [POST]
func GetAllConversationsByAccount(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForGetConversationsByAccount
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Conversations by Account Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		accountID, err := uuid.Parse(req.AccountID)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Conversations by Account - Invalid Account ID",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid account ID",
				"status": 400,
			})
			return
		}

		page := req.Page
		per_page := req.PerPage
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.GetAllConversationsByAccount(c, accountID, page, per_page, req.AccountType)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Conversations by Account Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Conversations by Account",
			Message:   "Conversations fetched successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Messages by Conversation ID
// @Description Get all messages for a specific conversation
// @Tags Admin Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForGetMessagesByConversation true "request payload for get messages by conversation"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/conversation/messages [POST]
func GetMessagesByConversationID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForGetMessagesByConversation
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Messages by Conversation ID Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		conversationID, err := uuid.Parse(req.ConversationID)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Messages by Conversation ID - Invalid Conversation ID",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid conversation ID",
				"status": 400,
			})
			return
		}

		page := req.Page
		per_page := req.PerPage
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.GetMessagesByConversationID(c, conversationID, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Messages by Conversation ID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get Messages by Conversation ID",
			Message:   "Messages fetched successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
