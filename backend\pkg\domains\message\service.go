package message

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"
)

type Service interface {
	SendMessage(ctx context.Context, req *dtos.SendMessageRequest) (*dtos.MessageResponse, error)
	GetMessagesByConversationID(ctx context.Context, conversation_id uuid.UUID, page, per_page int) (*dtos.PaginatedData, error)
	MarkMessagesAsRead(ctx context.Context, req *dtos.MarkAsReadRequest) error

	StartConversation(ctx context.Context, req *dtos.StartConversationRequest) (*dtos.ConversationResponse, error)
	DeleteConversation(ctx context.Context, conversationID uuid.UUID) error
	GetConversations(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	GetConversation(ctx context.Context, conversationID uuid.UUID) (*dtos.ConversationResponse, error)

	GenerateSubscribeToken(ctx context.Context) (*dtos.CentrifugoTokenResponse, error)
}

type service struct {
	repository Repository
	centrifugo *utils.CentrifugoWrapper
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
		centrifugo: utils.InitCentrifugo(),
	}
}

// SendMessage sends a new message
func (s *service) SendMessage(ctx context.Context, req *dtos.SendMessageRequest) (*dtos.MessageResponse, error) {
	receiverAccountType := 1
	if state.GetCurrentAccountType(ctx) == 1 {
		receiverAccountType = 2
	}

	// Find or create conversation
	var conversation *entities.Conversation
	var err error

	if state.GetCurrentAccountType(ctx) == 1 { // Customer sending to cleaner
		conversation, err = s.repository.GetConversationByParticipants(ctx, state.GetCurrentID(ctx), req.ReceiverID)
	} else { // Cleaner sending to customer
		conversation, err = s.repository.GetConversationByParticipants(ctx, req.ReceiverID, state.GetCurrentID(ctx))
	}

	if err != nil {
		// -----> Create new conversation
		conversation = &entities.Conversation{
			Base: entities.Base{
				ID: uuid.New(),
			},
		}

		if state.GetCurrentAccountType(ctx) == 1 {
			conversation.CustomerID = state.GetCurrentID(ctx)
			conversation.CleanerID = req.ReceiverID
		} else {
			conversation.CustomerID = req.ReceiverID
			conversation.CleanerID = state.GetCurrentID(ctx)
		}

		// -----> generate new channel name
		conversation.ChannelName = entities.GenerateChannelName(conversation.CustomerID, conversation.CleanerID)

		if err := s.repository.CreateConversation(ctx, conversation); err != nil {
			return nil, fmt.Errorf("send message error while create conversation: %w", err)
		}
	}

	// Create message
	message := &entities.Message{
		Base: entities.Base{
			ID: uuid.New(),
		},
		SenderID:    state.GetCurrentID(ctx),
		ReceiverID:  req.ReceiverID,
		Content:     req.Content,
		MessageType: req.MessageType,
		FileURL:     req.FileURL,
		FileName:    req.FileName,
		ChannelName: conversation.ChannelName,
		IsRead:      false,
	}

	if message.MessageType == "" {
		message.MessageType = "text"
	}

	// Save message to database
	if err := s.repository.CreateMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("send message error while create message: %w", err)
	}

	// Update conversation last message
	if err := s.repository.UpdateConversationLastMessage(ctx, conversation.ID, message.ID); err != nil {
		return nil, fmt.Errorf("send message error while update conversation: %w", err)
	}

	// Get full message with sender info for response
	fullMessage, err := s.repository.GetMessageByID(ctx, message.ID)
	if err != nil {
		return nil, fmt.Errorf("send message error while get message: %w", err)
	}

	// Convert to response DTO with sender info
	messageResponse := s.messageToResponseWithSender(ctx, fullMessage, state.GetCurrentAccountType(ctx))

	// Send via Centrifugo
	if err := s.centrifugo.SendPrivateMessage(ctx, state.GetCurrentID(ctx), req.ReceiverID, messageResponse); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Failed to send message via Centrifugo: %v\n", err)
		return messageResponse, nil
	}

	// // Notify both users about conversation update
	// conversationResponse := s.conversationToResponse(conversation, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx))
	// if err := s.centrifugo.NotifyConversationUpdate(ctx, state.GetCurrentID(ctx), conversationResponse); err != nil {
	// 	fmt.Printf("Failed to notify sender about conversation update: %v\n", err)
	// }

	receiverConversationResponse := s.conversationToResponse(conversation, req.ReceiverID, receiverAccountType)
	if err := s.centrifugo.NotifyConversationUpdate(ctx, req.ReceiverID, receiverConversationResponse); err != nil {
		fmt.Printf("Failed to notify receiver about conversation update: %v\n", err)
	}

	return messageResponse, nil
}

func (s *service) GetMessagesByConversationID(ctx context.Context, conversation_id uuid.UUID, page, per_page int) (*dtos.PaginatedData, error) {
	// -----> Check if user is part of the conversation
	isInConversation, err := s.repository.IsUserInConversation(ctx, conversation_id, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx))
	if err != nil {
		return nil, fmt.Errorf("failed to check conversation access: %w", err)
	}

	if !isInConversation {
		return nil, fmt.Errorf("message_conversation_access_error")
	}

	messages, total, err := s.repository.GetMessagesByConversation(ctx, conversation_id, page, per_page)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs with sender info
	var responses []*dtos.MessageResponse
	for _, message := range messages {
		// Determine sender account type based on sender ID
		senderAccountType := 1 // default to customer
		if state.GetCurrentAccountType(ctx) == 1 && message.SenderID != state.GetCurrentID(ctx) {
			senderAccountType = 2 // other person is cleaner
		} else if state.GetCurrentAccountType(ctx) == 2 && message.SenderID != state.GetCurrentID(ctx) {
			senderAccountType = 1 // other person is customer
		} else {
			senderAccountType = state.GetCurrentAccountType(ctx) // sender is current user
		}

		responses = append(responses, s.messageToResponseWithSender(ctx, message, senderAccountType))
	}

	// Calculate pagination metadata
	totalPages := int(total) / per_page
	if int(total)%per_page != 0 {
		totalPages++
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      total,
		TotalPages: totalPages,
		IsLastPage: page >= totalPages,
		Rows:       responses,
	}, nil
}

// MarkMessagesAsRead marks messages as read
func (s *service) MarkMessagesAsRead(ctx context.Context, req *dtos.MarkAsReadRequest) error {
	// -----> Check if user is part of the conversation
	isInConversation, err := s.repository.IsUserInConversation(ctx, req.ConversationID, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx))
	if err != nil {
		return fmt.Errorf("failed to check conversation access: %w", err)
	}

	if !isInConversation {
		return fmt.Errorf("message_conversation_access_error")
	}

	return s.repository.MarkMessagesAsRead(ctx, req.ConversationID, state.GetCurrentID(ctx))
}

func (s *service) StartConversation(ctx context.Context, req *dtos.StartConversationRequest) (*dtos.ConversationResponse, error) {
	// -----> Determine participant roles
	var customer_id, cleaner_id uuid.UUID

	if state.GetCurrentAccountType(ctx) == 1 {
		customer_id = state.GetCurrentID(ctx)
		cleaner_id = req.ParticipantID
	} else {
		customer_id = req.ParticipantID
		cleaner_id = state.GetCurrentID(ctx)
	}

	// -----> Check if conversation already exists
	existingConversation, err := s.repository.GetConversationByParticipants(ctx, customer_id, cleaner_id)
	if err == nil {
		// -----> Conversation exists, return it
		return s.conversationToResponse(existingConversation, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx)), nil
	}

	// -----> Create new conversation
	conversation := &entities.Conversation{
		Base: entities.Base{
			ID: uuid.New(),
		},
		CustomerID:  customer_id,
		CleanerID:   cleaner_id,
		ChannelName: entities.GenerateChannelName(customer_id, cleaner_id),
	}

	// TODO: if we have transaction, can be better
	if err := s.repository.CreateConversation(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to create conversation: %w", err)
	}

	// -----> Send initial message if provided
	if req.InitialMessage != "" {
		sendMessageReq := &dtos.SendMessageRequest{
			ReceiverID:  req.ParticipantID,
			Content:     req.InitialMessage,
			MessageType: "text",
		}

		_, err := s.SendMessage(ctx, sendMessageReq)
		if err != nil {
			return nil, fmt.Errorf("failed to send initial message: %w", err)
		}
	}

	// -----> Get updated conversation
	updated_conversation, err := s.repository.GetConversationByID(ctx, conversation.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated conversation: %w", err)
	}

	return s.conversationToResponse(updated_conversation, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx)), nil
}

func (s *service) GetConversations(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	resp, err := s.repository.GetUserConversations(ctx, page, per_page)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversations: %w", err)
	}

	return resp, nil
}

func (s *service) DeleteConversation(ctx context.Context, conversationID uuid.UUID) error {
	return s.repository.DeleteConversation(ctx, conversationID)
}

// GetConversation retrieves a specific conversation
func (s *service) GetConversation(ctx context.Context, conversationID uuid.UUID) (*dtos.ConversationResponse, error) {
	// Check if user is part of the conversation
	isInConversation, err := s.repository.IsUserInConversation(ctx, conversationID, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx))
	if err != nil {
		return nil, fmt.Errorf("failed to check conversation access: %w", err)
	}

	if !isInConversation {
		return nil, fmt.Errorf("user not authorized to access this conversation")
	}

	conversation, err := s.repository.GetConversationByID(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	response := s.conversationToResponse(conversation, state.GetCurrentID(ctx), state.GetCurrentAccountType(ctx))

	// Get unread count
	unreadCount, err := s.repository.GetUnreadMessageCount(ctx, conversationID, state.GetCurrentID(ctx))
	if err == nil {
		response.UnreadCount = int(unreadCount)
	}

	return response, nil
}

// -----> Generates a Centrifugo subscription token
func (s *service) GenerateSubscribeToken(ctx context.Context) (*dtos.CentrifugoTokenResponse, error) {

	var channels []string
	conversations, err := s.repository.GetUserConversationsWithoutPagination(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user conversations: %w", err)
	}

	for _, v := range conversations {
		channels = append(channels, v.ChannelName)
	}

	token, err := s.centrifugo.GenerateSubscriptionToken(ctx, channels)
	if err != nil {
		return nil, fmt.Errorf("failed to generate connection token: %w", err)
	}

	return &dtos.CentrifugoTokenResponse{
		Token: token,
	}, nil
}

// Helper methods

// messageToResponse converts a message entity to response DTO
func (s *service) messageToResponse(message *entities.Message) *dtos.MessageResponse {
	response := &dtos.MessageResponse{
		ID:          message.ID,
		SenderID:    message.SenderID,
		ReceiverID:  message.ReceiverID,
		Content:     message.Content,
		MessageType: message.MessageType,
		FileURL:     message.FileURL,
		FileName:    message.FileName,
		IsRead:      message.IsRead,
		ReadAt:      message.ReadAt,
		ChannelName: message.ChannelName,
		CreatedAt:   message.CreatedAt,
		UpdatedAt:   message.UpdatedAt,
	}

	return response
}

// messageToResponseWithSender converts a message entity to response DTO with sender info
func (s *service) messageToResponseWithSender(ctx context.Context, message *entities.Message, senderAccountType int) *dtos.MessageResponse {
	response := s.messageToResponse(message)

	// Get sender info
	name, email, photoURL, err := s.repository.GetUserInfo(ctx, message.SenderID, senderAccountType)
	if err == nil {
		response.SenderName = name
		response.SenderEmail = email
		response.SenderPhotoURL = photoURL
	}

	return response
}

// conversationToResponse converts a conversation entity to response DTO
func (s *service) conversationToResponse(conversation *entities.Conversation, currentUserID uuid.UUID, currentUserAccountType int) *dtos.ConversationResponse {
	response := &dtos.ConversationResponse{
		ID:            conversation.ID,
		CustomerID:    conversation.CustomerID,
		CleanerID:     conversation.CleanerID,
		ChannelName:   conversation.ChannelName,
		LastMessageAt: conversation.LastMessageAt,
		CreatedAt:     conversation.CreatedAt,
		UpdatedAt:     conversation.UpdatedAt,
	}

	// Add last message if available
	if conversation.LastMessage != nil {
		// Determine last message sender account type
		lastMessageSenderAccountType := 1 // default to customer
		if conversation.LastMessage.SenderID == conversation.CleanerID {
			lastMessageSenderAccountType = 2
		}
		response.LastMessage = s.messageToResponseWithSender(context.Background(), conversation.LastMessage, lastMessageSenderAccountType)
	}

	// Add participant info (the other person in the conversation)
	var participantID uuid.UUID
	var participantAccountType int

	if currentUserAccountType == 1 { // Current user is customer, show cleaner info
		participantID = conversation.CleanerID
		participantAccountType = 2
	} else { // Current user is cleaner, show customer info
		participantID = conversation.CustomerID
		participantAccountType = 1
	}

	// Get participant info
	name, email, photoURL, err := s.repository.GetUserInfo(context.Background(), participantID, participantAccountType)
	if err == nil {
		response.ParticipantName = name
		response.ParticipantEmail = email
		response.ParticipantPhotoURL = photoURL
	}

	return response
}
