package cleaner

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/cleaner"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Get Languages
// @Description Get Languages
// @Tags Cleaner-Language
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} dtos.GetLanguagesResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/language [GET]
func GetLanguages(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetLanguages(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Languages Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Languages",
			Message:     "Languages Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Create Language
// @Description Create Language
// @Tags Cleaner-Language
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateLanguage true "request payload for create language"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/language [POST]
func CreateLanguage(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateLanguage
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Create Language Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		if err := s.CreateLanguage(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Create Language Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Url:         c.Request.URL.Path,
				Ip:          state.GetCurrentUserIP(c),
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "already_exist_language" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("already_exist_language", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:       "Create Language",
			Message:     "Language Created Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})
		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_create", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Delete Language
// @Description Delete Language
// @Tags Cleaner-Language
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path string true "language id"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/language/{id} [DELETE]
func DeleteLanguage(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Language Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		if err := s.DeleteLanguage(c, parsed_id); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Language Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_language" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_language", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Language",
			Message:     "Language Deleted Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Get Work Experiences
// @Description Get Work Experiences
// @Tags Cleaner-WorkExperience
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} dtos.GetWorkExperiencesResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/work-experience [GET]
func GetWorkExperiences(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetWorkExperiences(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Work Experiences Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_work_experience" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_work_experience", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Work Experiences",
			Message:     "Work Experiences Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Work Experience
// @Description Get Work Experience by ID
// @Tags Cleaner-WorkExperience
// @Security BearerAuth
// @Produce  json
// @Param id path string true "Work Experience ID"
// @Success 200 {object} dtos.GetWorkExperienceResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/work-experience/{id} [GET]
func GetWorkExperience(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Work Experience Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		resp, err := s.GetWorkExperience(c, id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Work Experience Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_work_experience" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_work_experience", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Work Experience",
			Message:     "Work Experience Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Create Work Experience
// @Description Create Work Experience
// @Tags Cleaner-WorkExperience
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param request body dtos.RequestForCreateWorkExperience true "Work Experience data"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/work-experience [POST]
func CreateWorkExperience(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateWorkExperience
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Create Work Experience Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		err := s.CreateWorkExperience(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Create Work Experience Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "already_exist_work_experience" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("already_exist_work_experience", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Create Work Experience",
			Message:     "Work Experience Created Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_create", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Update Work Experience
// @Description Update Work Experience
// @Tags Cleaner-WorkExperience
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param request body dtos.RequestForUpdateWorkExperience true "Work Experience data"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/work-experience/{id} [PUT]
func UpdateWorkExperience(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateWorkExperience
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Update Work Experience Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		req.ID = c.Param("id")

		err := s.UpdateWorkExperience(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Update Work Experience Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_work_experience" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_work_experience", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_update", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Update Work Experience",
			Message:     "Work Experience Updated Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_update", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Delete Work Experience
// @Description Delete Work Experience
// @Tags Cleaner-WorkExperience
// @Security BearerAuth
// @Produce  json
// @Param id path string true "Work Experience ID"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/work-experience/{id} [DELETE]
func DeleteWorkExperience(s cleaner.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Work Experience Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_parse", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		err = s.DeleteWorkExperience(c, id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Work Experience Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_work_experience" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_work_experience", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Work Experience",
			Message:     "Work Experience Deleted Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}
