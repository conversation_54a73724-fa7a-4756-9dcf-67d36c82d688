package routes

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/profil"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"
)

// @Summary Update Customer Profile
// @Description Update Customer Profile
// @Tags Common-Profile
// @Security BearerAuth
// @Accept  multipart/form-data
// @Produce  json
// @Param date_of_birth formData string true "Date of birth" example("1990-01-01")
// @Param phone formData string true "Phone number" example("+************")
// @Param country formData string true "Country" example("Turkey")
// @Param name formData string true "Name" example("<PERSON>")
// @Param surname formData string true "Surname" example("Doe")
// @Param profile_photo formData file false "Profile photo"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/profile [POST]
func UpdateProfile(s profil.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		dateOfBirth := c.PostForm("date_of_birth")
		phone := c.PostForm("phone")
		country := c.PostForm("country")
		name := c.PostForm("name")
		surname := c.PostForm("surname")
		referenceID := c.PostForm("reference_id")

		var title, description, availableDates string
		if state.GetCurrentAccountType(c) == 2 {
			title = c.PostForm("title")
			description = c.PostForm("description")
			availableDates = c.PostForm("available_dates")
		}

		if dateOfBirth == "" || country == "" || name == "" || surname == "" {
			mainlog.CreateLog(&entities.Log{
				Title:       "Update Profile Validation Error",
				Message:     "Missing required fields: date_of_birth, name, surname or country",
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		// Create request object
		req := dtos.RequestForProfileUpdate{
			Title:       title,
			Description: description,
			DateOfBirth: dateOfBirth,
			Phone:       phone,
			Country:     country,
			Name:        name,
			Surname:     surname,
			ReferenceID: referenceID,
		}

		if state.GetCurrentAccountType(c) == 2 {
			var availableDatesSlice []string
			if availableDates != "" {
				dates := strings.Split(availableDates, ",")
				for _, date := range dates {
					trimmedDate := strings.TrimSpace(date)
					if trimmedDate != "" {
						availableDatesSlice = append(availableDatesSlice, trimmedDate)
					}
				}
			}
			req.AvailableDates = availableDatesSlice
			req.Title = title
			req.Description = description
		}

		// Handle profile photo upload if provided
		file, header, err := c.Request.FormFile("profile_photo")
		if err == nil && file != nil {
			defer file.Close()

			contentType := header.Header.Get("Content-Type")
			if !strings.HasPrefix(contentType, "image/") {
				mainlog.CreateLog(&entities.Log{
					Title:       "Update Profile File Type Error",
					Message:     "Invalid file type: " + contentType,
					Type:        "error",
					Proto:       "http",
					Ip:          state.GetCurrentUserIP(c),
					Url:         c.Request.URL.Path,
					OS:          state.GetCurrentOS(c),
					AccountID:   state.GetCurrentID(c),
					AccountType: state.GetCurrentAccountType(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("error-file-upload-only_image_files_are_allowed", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}

			profilePhotoURL, _, err := utils.UploadProfilePhoto(file, header.Size, contentType, header.Filename)
			if err != nil {
				mainlog.CreateLog(&entities.Log{
					Title:       "Update Profile Photo Upload Error",
					Message:     "Error: " + err.Error(),
					Type:        "error",
					Proto:       "http",
					Ip:          state.GetCurrentUserIP(c),
					Url:         c.Request.URL.Path,
					OS:          state.GetCurrentOS(c),
					AccountID:   state.GetCurrentID(c),
					AccountType: state.GetCurrentAccountType(c),
				})
				c.AbortWithStatusJSON(500, gin.H{
					"error":  localizer.GetTranslated("error-file-upload-profile-photo", state.GetCurrentPhoneLanguage(c), nil),
					"status": 500,
				})
				return
			}

			req.ProfilePhotoURL = profilePhotoURL
		}

		if err := s.UpdateProfile(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Update Profile Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_account_type" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_account_type", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_customer" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_customer", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_cleaner" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_cleaner", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "reference_id_already_used_for_this_account" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("reference_id_already_used_for_this_account", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if strings.Contains(err.Error(), "error_reference_processing") {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("error_reference_processing", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_update", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Update Profile",
			Message:     "Customer Profile Updated Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_update", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Get Customer Profile
// @Description Get Customer Profile
// @Tags Common-Profile
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} entities.ResponseForDetail
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/my-profile [GET]
func GetMyProfile(s profil.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if state.GetCurrentAccountType(c) == 1 {
			customer, err := s.GetMyCustomerProfile(c)
			if err != nil {
				mainlog.CreateLog(&entities.Log{
					Title:       "Get Customer Profile Error",
					Message:     "Error: " + err.Error(),
					Type:        "error",
					Proto:       "http",
					Ip:          state.GetCurrentUserIP(c),
					Url:         c.Request.URL.Path,
					OS:          state.GetCurrentOS(c),
					AccountID:   state.GetCurrentID(c),
					AccountType: state.GetCurrentAccountType(c),
				})
				c.AbortWithStatusJSON(500, gin.H{
					"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
					"status": 500,
				})
				return
			}
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Customer Profile",
				Message:     "Customer Profile Get Successfully",
				Type:        "info",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.JSON(200, gin.H{
				"data":   customer,
				"status": 200,
			})
			return
		} else if state.GetCurrentAccountType(c) == 2 {
			cleaner, err := s.GetMyCleanerProfile(c)
			if err != nil {
				mainlog.CreateLog(&entities.Log{
					Title:       "Get Cleaner Profile Error",
					Message:     "Error: " + err.Error(),
					Type:        "error",
					Proto:       "http",
					Ip:          state.GetCurrentUserIP(c),
					Url:         c.Request.URL.Path,
					OS:          state.GetCurrentOS(c),
					AccountID:   state.GetCurrentID(c),
					AccountType: state.GetCurrentAccountType(c),
				})
				c.AbortWithStatusJSON(500, gin.H{
					"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
					"status": 500,
				})
				return
			}
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Cleaner Profile",
				Message:     "Cleaner Profile Get Successfully",
				Type:        "info",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})

			c.JSON(200, gin.H{
				"data":   cleaner,
				"status": 200,
			})
			return
		} else {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Profile Error",
				Message:     "Not Found Account Type",
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("not_found_account_type", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
	}
}

// @Summary Get Profile By ID
// @Description Get Profile By ID
// @Tags Common-Profile
// @Security BearerAuth
// @Produce  json
// @Param id query string true "profile id"
// @Param account_type query int true "account type"
// @Success 200 {object} entities.ResponseForDetail
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/profile [GET]
func GetProfileByID(s profil.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// TODO: account_type query parametre olarak gelmeli
		parsed_id, err := uuid.Parse(c.Query("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Profile Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		account_type, err := strconv.Atoi(c.Query("account_type"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Profile Parse Account Type Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		resp, err := s.GetProfileByID(c, account_type, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Profile Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_account_type" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_account_type", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:       "Get Profile",
			Message:     "Profile Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
