package message

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/message"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"
)

// @Summary Get Centrifugo Connection Token
// @Description Get a JWT token for Centrifugo WebSocket connection
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} dtos.CentrifugoTokenResponseForSwagger
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /centrifugo/subscribe-token [GET]
func GetSubscribeCentrifugoToken(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GenerateSubscribeToken(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Subscribe Centrifugo Token Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Subscribe Centrifugo Token",
			Message:     "Centrifugo Token Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Start Conversation
// @Description Start a new conversation with another user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.StartConversationRequest true "Start conversation request"
// @Success 201 {object} dtos.ConversationsResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /conversation/start [POST]
func StartConversation(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.StartConversationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Start Conversation Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		conversation_resp, err := s.StartConversation(c, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Start Conversation Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Start Conversation",
			Message:     "Conversation Started Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   conversation_resp,
			"status": 201,
		})
	}
}

// @Summary Delete Conversation
// @Description Delete a conversation
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Conversation ID"
// @Success 200 {object} dtos.GetResponseStatusOK
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /conversation/{id} [DELETE]
func DeleteConversation(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Conversation Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		if err := s.DeleteConversation(c, parsed_id); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Conversation Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_conversation" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_conversation", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Conversation",
			Message:     "Conversation Deleted Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Get Conversations
// @Description Get all conversations for the current user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(20)
// @Success 200 {object} dtos.PaginateResponseForSwagger
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /conversation [GET]
func GetConversations(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.GetConversations(c, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Conversations Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Conversations",
			Message:     "Conversations Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Conversation
// @Description Get a specific conversation by ID
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Conversation ID"
// @Success 200 {object} dtos.ConversationsResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /conversation/{id} [GET]
func GetConversation(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Conversation Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.GetConversation(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Conversation Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Conversation",
			Message:     "Conversation Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// -----> Message Endpoints Start

// @Summary Send Message
// @Description Send a message to another user with optional file/image upload
// @Tags Message
// @Security BearerAuth
// @Accept multipart/form-data
// @Produce json
// @Param receiver_id formData string true "Receiver ID (UUID)"
// @Param content formData string true "Message content"
// @Param message_type formData string false "Message type (text, image, file)" default(text)
// @Param file formData file false "File or image to upload"
// @Success 201 {object} dtos.MessagesResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /message/send [POST]
func SendMessage(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Parse form data
		receiverIDStr := c.PostForm("receiver_id")
		content := c.PostForm("content")
		messageType := c.PostForm("message_type")

		if receiverIDStr == "" {
			mainlog.CreateLog(&entities.Log{
				Title:       "Send Message Validation Error",
				Message:     "receiver_id is required",
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		receiverID, err := uuid.Parse(receiverIDStr)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Send Message Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		if messageType == "" {
			messageType = "text"
		}

		req := dtos.SendMessageRequest{
			ReceiverID:  receiverID,
			Content:     content,
			MessageType: messageType,
		}

		file, header, err := c.Request.FormFile("file")
		if err == nil && file != nil {
			defer file.Close()

			contentType := header.Header.Get("Content-Type")
			// fileExt := strings.ToLower(filepath.Ext(header.Filename))

			if messageType == "image" {
				if !strings.HasPrefix(contentType, "image/") {
					mainlog.CreateLog(&entities.Log{
						Title:       "Send Message File Type Error",
						Message:     "Invalid file type for image: " + contentType,
						Type:        "error",
						Proto:       "http",
						Ip:          state.GetCurrentUserIP(c),
						Url:         c.Request.URL.Path,
						OS:          state.GetCurrentOS(c),
						AccountID:   state.GetCurrentID(c),
						AccountType: state.GetCurrentAccountType(c),
					})
					c.AbortWithStatusJSON(400, gin.H{
						"error":  localizer.GetTranslated("error-file-upload-only_image_files_are_allowed", state.GetCurrentPhoneLanguage(c), nil),
						"status": 400,
					})
					return
				}
			}

			fileURL, fileName, err := utils.UploadMessageFile(file, header.Size, contentType, header.Filename)
			if err != nil {
				mainlog.CreateLog(&entities.Log{
					Title:       "Send Message File Upload Error",
					Message:     "Error: " + err.Error(),
					Type:        "error",
					Proto:       "http",
					Ip:          state.GetCurrentUserIP(c),
					Url:         c.Request.URL.Path,
					OS:          state.GetCurrentOS(c),
					AccountID:   state.GetCurrentID(c),
					AccountType: state.GetCurrentAccountType(c),
				})
				c.AbortWithStatusJSON(500, gin.H{
					"error":  localizer.GetTranslated("error_process", state.GetCurrentPhoneLanguage(c), nil),
					"status": 500,
				})
				return
			}

			req.FileURL = fileURL
			req.FileName = fileName
		} else {
			if content == "" {
				c.AbortWithStatusJSON(500, gin.H{
					"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
					"status": 500,
				})
				return
			}
		}

		response, err := s.SendMessage(c, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Send Message Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Send Message",
			Message:     "Message Sent Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   response,
			"status": 201,
		})
	}
}

// @Summary Get Messages
// @Description Get messages for a conversation with pagination
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param conversation_id path string true "Conversation ID"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(20)
// @Success 200 {object} dtos.PaginateResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /message/{conversation_id} [GET]
func GetMessagesByConversationID(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 20
		}
		parsed_conversation_id, err := uuid.Parse(c.Param("conversation_id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Messages By Conversation ID Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		resp, err := s.GetMessagesByConversationID(c, parsed_conversation_id, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Messages By Conversation ID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "message_conversation_access_error" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("message_conversation_access_error", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_conversation" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_conversation", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Messages By Conversation ID",
			Message:     "Messages By Conversation ID Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Mark Messages as Read
// @Description Mark messages in a conversation as read
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.MarkAsReadRequest true "Mark as read request"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /message/read [POST]
func MarkMessagesAsRead(s message.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.MarkAsReadRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Mark Messages as Read Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		err := s.MarkMessagesAsRead(c, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Mark Messages as Read Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "message_conversation_access_error" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("message_conversation_access_error", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_conversation" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_conversation", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_process", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Mark Messages as Read",
			Message:     "Messages Marked as Read Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("message_marked_as_read", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// Message Endpoints End <-----
