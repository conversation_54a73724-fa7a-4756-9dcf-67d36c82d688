package routes

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/order"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Create Order
// @Description Create Order
// @Tags Common-Order
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateOrder true "request payload for create order"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/order [POST]
func CreateOrder(s order.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateOrder
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Create Order Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		resp, err := s.CreateOrder(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Create Order Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_service_category" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_service_category", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "you_cannot_create_order_with_this_license" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("you_cannot_create_order_with_this_license", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_address" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_address", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_start_date_cannot_be_less_than_x_hour_after_current_time" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_start_date_cannot_be_less_than_x_hour_after_current_time", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_start_date_cannot_be_before_current_time" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_start_date_cannot_be_before_current_time", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_end_date_cannot_be_before_start_time" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_end_date_cannot_be_before_start_time", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_duration_cannot_be_less_than_min_duration" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_duration_cannot_be_less_than_min_duration", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}

			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_create", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Create Order",
			Message:     "Order Created Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get Orders
// @Description Get Orders
// @Tags Common-Order
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param radius_meters query int true "radius meters number"
// @Param account_type query int true "account type number if you don't send an account_type, it will return orders for the opposite account type"
// @Param min_range query int false "min range number"
// @Param max_range query int false "max range number"
// @Param start_date query string false "start date"
// @Param end_date query string false "end date"
// @Param room_number query string false "room number"
// @Param account_point query int false "account point number"
// @Param order_type query string false "order type"
// @Param search query string false "search"
// @Success 200 {object} dtos.PaginateResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/order [GET]
func GetOrders(s order.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		account_type, _ := strconv.Atoi(c.Query("account_type"))
		start_date := c.Query("start_date")
		end_date := c.Query("end_date")
		room_number := c.Query("room_number")
		account_point, _ := strconv.Atoi(c.Query("account_point")) // its just for customer
		radius_meters, _ := strconv.ParseFloat(c.Query("radius_meters"), 64)
		order_type := c.Query("order_type")
		search := c.Query("search")
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}
		if account_type == 0 {
			current_account_type := state.GetCurrentAccountType(c)
			switch current_account_type {
			case 1:
				account_type = 2
			case 2:
				account_type = 1
			}
		}
		min_rage, err := strconv.ParseFloat(c.Query("min_range"), 64)
		if err != nil {
			min_rage = 0
		}
		max_range, err := strconv.ParseFloat(c.Query("max_range"), 64)
		if err != nil {
			max_range = 0
		}
		resp, err := s.GetOrders(c, page, per_page, account_type, account_point, radius_meters, min_rage, max_range, start_date, end_date, room_number, order_type, search)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Orders Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Orders",
			Message:     "Orders Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get My Orders
// @Description Get My Orders
// @Tags Common-Order
// @Security BearerAuth
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Success 200 {object} dtos.PaginateResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/my-order [GET]
func GetMyOrders(s order.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.GetMyOrders(c, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Orders Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:       "Get Orders",
			Message:     "Orders Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Order By ID
// @Description Get Order By ID
// @Tags Common-Order
// @Security BearerAuth
// @Produce  json
// @Param id path string true "order id"
// @Success 200 {object} entities.OrderResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/order/{id} [GET]
func GetOrder(s order.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Order Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		resp, err := s.GetOrder(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Order Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})

			if err.Error() == "not_found_order" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_order", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "you_cannot_see_this_order_with_this_license" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("you_cannot_see_this_order_with_this_license", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Order",
			Message:     "Order Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete Order
// @Description Delete Order
// @Tags Common-Order
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/order/{id} [DELETE]
func DeleteOrder(s order.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Order Parse UUID Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		if err := s.DeleteOrder(c, parsed_id); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Delete Order Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_order" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_order", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Order",
			Message:     "Order Deleted Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Update Order
// @Description Update Order
// @Tags Common-Order
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateOrder true "request payload for update order"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/order/{id} [PUT]
func UpdateOrder(s order.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateOrder
		req.ID = c.Param("id")
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Update Order Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		if err := s.UpdateOrder(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Update Order Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_order" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_order", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_address" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_address", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "not_found_service_category" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_service_category", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "you_cannot_create_order_with_this_license" {
				c.AbortWithStatusJSON(500, gin.H{
					"error":  localizer.GetTranslated("you_cannot_create_order_with_this_license", state.GetCurrentPhoneLanguage(c), nil),
					"status": 500,
				})
				return
			}
			if err.Error() == "time_error_start_date_cannot_be_less_than_x_hour_after_current_time" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_start_date_cannot_be_less_than_x_hour_after_current_time", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_start_date_cannot_be_before_current_time" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_start_date_cannot_be_before_current_time", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_end_date_cannot_be_before_start_time" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_end_date_cannot_be_before_start_time", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			if err.Error() == "time_error_duration_cannot_be_less_than_min_duration" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("time_error_duration_cannot_be_less_than_min_duration", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_update", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:       "Update Order",
			Message:     "Order Updated Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_update", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}
