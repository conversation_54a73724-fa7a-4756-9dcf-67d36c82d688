package admin

import (
	"context"
	"errors"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/cache"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	// Auth
	adminLogin(ctx context.Context, req dtos.RequestForLogin) (*dtos.AuthenticationResponse, error)
	adminLogout(ctx context.Context, adminID string) error

	serviceCategoryGetAll(ctx context.Context, page, per_page int, approved int, is_main bool) (*dtos.PaginatedData, error)
	serviceCategoryCreate(ctx context.Context, req dtos.RequestForCreateServiceCategory) error
	serviceCategoryUpdate(ctx context.Context, req dtos.RequestForUpdateServiceCategory) error
	serviceCategoryDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error

	commentGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error)
	commentDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error

	blogCreate(ctx context.Context, req dtos.RequestForCreateBlog) error
	blogDelete(ctx context.Context, req dtos.RequestForDeleteBlog) error
	blogUpdate(ctx context.Context, req dtos.RequestForUpdateBlog) error
	blogGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error)
	blogGetByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error)

	orderGetAll(ctx context.Context, req dtos.RequestForOrderGetAllByID) (*dtos.PaginatedData, error)
	orderGetByID(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error)
	orderDelete(ctx context.Context, id uuid.UUID) error

	preferenceCreate(ctx context.Context, req dtos.RequestForCreatePreference) error
	preferenceUpdate(ctx context.Context, req dtos.RequestForUpdatePreference) error
	preferenceDelete(ctx context.Context, req dtos.RequestForDeletePreference) error
	preferenceGetByID(ctx context.Context, id uuid.UUID) (*entities.PreferenceResponse, error)
	preferenceGetAll(ctx context.Context, req dtos.RequestForPaginate) (*dtos.PaginatedData, error)

	newVersion(ctx context.Context, req dtos.RequestForNewVersion) error

	verificationApprovedTC(ctx context.Context, id uuid.UUID) error

	customerGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	customerGetByID(ctx context.Context, id uuid.UUID) (*entities.ResponseForDetail, error)
	cleanerGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	cleanerGetByID(ctx context.Context, id uuid.UUID) (*entities.ResponseForDetail, error)

	// Conversation and Message management
	getAllConversationsByAccount(ctx context.Context, accountID uuid.UUID, page, per_page, account_type int) (*dtos.PaginatedData, error)
	getMessagesByConversationID(ctx context.Context, conversationID uuid.UUID, page, per_page int) (*dtos.PaginatedData, error)

	getDashboardStats(ctx context.Context) (*dtos.DashboardStatsResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// -----> Service Type Start

func (r *repository) serviceCategoryGetAll(ctx context.Context, page, per_page int, approved int, is_main bool) (*dtos.PaginatedData, error) {
	var (
		service_types []entities.ServiceCategory
		resp          []entities.ServiceCategoryResponse
		count         int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{})

	switch approved {
	case 1:
		base_query = base_query.Where("approved_by_admin = ?", true)
	case 2:
		base_query = base_query.Where("approved_by_admin = ?", false)
	}

	if is_main {
		base_query = base_query.Where("is_main = ?", true)
	} else {
		base_query = base_query.Where("is_main = ?", false)
	}

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&service_types).Error; err != nil {
		return nil, err
	}

	for _, v := range service_types {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) serviceCategoryCreate(ctx context.Context, req dtos.RequestForCreateServiceCategory) error {
	var service_category_control entities.ServiceCategory

	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("is_main = ?", req.IsMain).
		Where("service_name = ?", req.ServiceNameTR).
		First(&service_category_control)

	if service_category_control.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistService)
	}

	serviceType := &entities.ServiceCategory{
		ServiceNameTR:   req.ServiceNameTR,
		ServiceNameEN:   req.ServiceNameEN,
		Description:     req.Description,
		MinDuration:     req.MinDuration,
		ApprovedByAdmin: false,
	}

	if req.IsMain {
		serviceType.IsMain = true
		serviceType.MainCategoryID = uuid.Nil
	} else {
		var control entities.ServiceCategory
		r.db.WithContext(ctx).
			Model(&entities.ServiceCategory{}).
			Where("is_main = ?", true).
			Where("id = ?", req.MainCategoryID).
			First(&control)

		if control.ID == uuid.Nil {
			return errors.New(consts.NotFoundServiceCategory)
		}
		serviceType.MainCategoryID = uuid.MustParse(req.MainCategoryID)
		serviceType.IsMain = false
	}

	return r.db.
		WithContext(ctx).
		Create(serviceType).
		Error
}

func (r *repository) serviceCategoryUpdate(ctx context.Context, req dtos.RequestForUpdateServiceCategory) error {
	var service_category entities.ServiceCategory
	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("id = ?", req.ID).
		First(&service_category)

	if service_category.ID == uuid.Nil {
		return errors.New(consts.NotFoundServiceCategory)
	}

	service_category.ServiceNameTR = req.ServiceNameTR
	service_category.ServiceNameEN = req.ServiceNameEN
	service_category.Description = req.Description
	service_category.MinDuration = req.MinDuration
	service_category.ApprovedByAdmin = req.IsActive

	return r.db.
		WithContext(ctx).
		Save(&service_category).Error

}

func (r *repository) serviceCategoryDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error {
	var control entities.ServiceCategory
	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("id = ?", req.ID).
		First(&control)

	if control.ID == uuid.Nil {
		return errors.New(consts.NotFoundServiceCategory)
	}

	return r.db.WithContext(ctx).
		Where("id = ?", req.ID).
		Delete(&entities.ServiceCategory{}).Error
}

// Service Type End <-----

//-----> Comment Start

func (r *repository) commentGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error) {
	var (
		comments []entities.Comment
		resp     []entities.CommentResponse
		count    int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Comment{})

	switch approved {
	case 1:
		base_query = base_query.Where("approved_by_admin = ?", true)
	case 2:
		base_query = base_query.Where("approved_by_admin = ?", false)
	}

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&comments).Error; err != nil {
		return nil, err
	}

	for _, v := range comments {
		resp = append(resp, v.Response(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) commentDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error {
	var comment entities.Comment
	r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("id = ?", req.ID).
		First(&comment)

	if comment.ID == uuid.Nil {
		return errors.New(consts.NotFoundComment)
	}

	return r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Delete(&comment).Error
}

// Comment End <-----

//-----> Blog Start

func (r *repository) blogCreate(ctx context.Context, req dtos.RequestForCreateBlog) error {

	blog := &entities.Blog{
		Title:           req.Title,
		Content:         req.Content,
		Summary:         req.Summary,
		CoverImageURL:   req.ImageURL,
		AuthorID:        uuid.Nil,
		ApprovedByAdmin: false,
		ViewCount:       0,
		Tags:            req.Tags,
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Create(blog).Error

	return err
}

func (r *repository) blogDelete(ctx context.Context, req dtos.RequestForDeleteBlog) error {
	var current_blog entities.Blog
	r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Where("id = ?", req.ID).
		First(&current_blog)

	if current_blog.ID == uuid.Nil {
		return errors.New(consts.NotFoundBlog)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Delete(&current_blog).Error

	return err
}

func (r *repository) blogUpdate(ctx context.Context, req dtos.RequestForUpdateBlog) error {
	var blog entities.Blog
	r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Where("id = ?", req.ID).
		First(&blog)

	if blog.ID == uuid.Nil {
		return errors.New(consts.NotFoundBlog)
	}

	blog.Title = req.Title
	blog.Content = req.Content
	blog.Summary = req.Summary
	blog.CoverImageURL = req.ImageURL
	blog.ApprovedByAdmin = req.IsActive
	blog.Tags = req.Tags

	err := r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Where("id = ?", req.ID).
		Save(&blog).Error

	return err
}

func (r *repository) blogGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error) {
	var (
		blogs []entities.Blog
		resp  []entities.BlogResponse
		count int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Blog{})

	switch approved {
	case 1:
		base_query = base_query.Where("approved_by_admin = ?", true)
	case 2:
		base_query = base_query.Where("approved_by_admin = ?", false)
	}

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&blogs).Error; err != nil {
		return nil, err
	}

	for _, v := range blogs {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) blogGetByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error) {
	var blog entities.Blog
	err := r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Where("id = ?", id).
		First(&blog).Error

	resp := blog.Response()

	return &resp, err
}

// Blog End <-----

// -----> Order Start

func (r *repository) orderGetAll(ctx context.Context, req dtos.RequestForOrderGetAllByID) (*dtos.PaginatedData, error) {
	var (
		orders []entities.Order
		resp   []entities.OrderResponse
		count  int64
	)

	base_query := r.db.WithContext(ctx).Debug().
		Model(&entities.Order{}).
		Where("account_id = ?", req.AccountID).
		Where("account_type = ?", req.AccountType)

	if err := base_query.
		Limit(req.PerPage).
		Offset((req.Page - 1) * req.PerPage).
		Order("created_at desc").
		Find(&orders).Error; err != nil {
		return nil, err
	}

	for _, v := range orders {
		resp = append(resp, v.Response(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(req.Page),
		PerPage:    int64(req.PerPage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(req.PerPage))),
		IsLastPage: req.Page >= int(math.Ceil(float64(count)/float64(req.PerPage))),
		Rows:       resp,
	}, nil
}

func (r *repository) orderGetByID(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error) {
	var (
		current_order entities.Order
		resp          entities.OrderResponse
	)
	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", id).
		First(&current_order)

	if current_order.ID == uuid.Nil {
		return &resp, errors.New(consts.NotFoundOrder)
	}

	resp = current_order.Response(r.db)

	return &resp, nil
}

func (r *repository) orderDelete(ctx context.Context, id uuid.UUID) error {
	var current_order entities.Order
	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", id).
		First(&current_order)

	if current_order.ID == uuid.Nil {
		return errors.New(consts.NotFoundOrder)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Delete(&current_order).Error

	return err
}

// Order End <-----

// -----> Preferences Start

func (r *repository) preferenceCreate(ctx context.Context, req dtos.RequestForCreatePreference) error {
	var preference_control entities.Preference

	r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Where("name = ?", req.Name).
		First(&preference_control)

	if preference_control.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistService)
	}

	preference := &entities.Preference{
		Name:            req.Name,
		Description:     req.Description,
		ApprovedByAdmin: false,
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Create(preference).Error

	return err
}

func (r *repository) preferenceDelete(ctx context.Context, req dtos.RequestForDeletePreference) error {
	var current_preference entities.Preference
	r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Where("id = ?", req.ID).
		First(&current_preference)

	if current_preference.ID == uuid.Nil {
		return errors.New(consts.NotFoundCustomerPreference)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Delete(&current_preference).Error

	return err
}

func (r *repository) preferenceUpdate(ctx context.Context, req dtos.RequestForUpdatePreference) error {
	var preference entities.Preference
	r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Where("id = ?", req.ID).
		First(&preference)

	if preference.ID == uuid.Nil {
		return errors.New(consts.NotFoundCustomerPreference)
	}

	preference.Name = req.Name
	preference.Description = req.Description
	preference.ApprovedByAdmin = req.IsActive

	err := r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Where("id = ?", req.ID).
		Save(&preference).Error

	return err
}

func (r *repository) preferenceGetByID(ctx context.Context, id uuid.UUID) (*entities.PreferenceResponse, error) {
	var preference entities.Preference
	err := r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Where("id = ?", id).
		First(&preference).Error

	resp := preference.Response()

	return &resp, err
}

func (r *repository) preferenceGetAll(ctx context.Context, req dtos.RequestForPaginate) (*dtos.PaginatedData, error) {
	var (
		preferences []entities.Preference
		resp        []entities.PreferenceResponse
		count       int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Preference{})

	switch req.Approved {
	case 1:
		base_query = base_query.Where("approved_by_admin = ?", true)
	case 2:
		base_query = base_query.Where("approved_by_admin = ?", false)
	}

	if err := base_query.
		Limit(req.PerPage).
		Offset((req.Page - 1) * req.PerPage).
		Order("created_at desc").
		Find(&preferences).Error; err != nil {
		return nil, err
	}

	for _, v := range preferences {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(req.Page),
		PerPage:    int64(req.PerPage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(req.PerPage))),
		IsLastPage: req.Page >= int(math.Ceil(float64(count)/float64(req.PerPage))),
		Rows:       resp,
	}, nil
}

// Preferences End <-----

// -----> Version Start

func (r *repository) newVersion(ctx context.Context, req dtos.RequestForNewVersion) error {
	var new_version entities.Version
	new_version.AndroidVersionName = req.AndroidVersionName
	new_version.AndroidBuildNumber = req.AndroidBuildNumber
	new_version.AndroidForceUpdateBuildNumber = req.AndroidForceUpdateBuildNumber
	new_version.IosForceUpdateBuildNumber = req.IosForceUpdateBuildNumber
	new_version.IosVersionName = req.IosVersionName
	new_version.IosBuildNumber = req.IosBuildNumber
	new_version.IsForce = req.IsForce
	err := r.db.WithContext(ctx).
		Model(&entities.Version{}).
		Create(&new_version).Error
	return err
}

// Version End <-----

// -----> Verification Start

func (r *repository) verificationApprovedTC(ctx context.Context, id uuid.UUID) error {
	var cleaner entities.Cleaner
	if err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", id).
		First(&cleaner).Error; err != nil {
		return err
	}

	if cleaner.TCNo == "" {
		return errors.New("tc_not_found")
	}

	if cleaner.TCNoVerified {
		return errors.New("tc_already_verified")
	}

	cleaner.TCNoVerified = true

	if err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", id).
		Save(&cleaner).Error; err != nil {
		return err
	}

	return nil
}

// Verification End <-----

// -----> Customer Start

func (r *repository) customerGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		customers []entities.Customer
		resp      []entities.ResponseForDetail
		count     int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Customer{})

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&customers).Error; err != nil {
		return nil, err
	}

	for _, v := range customers {
		resp = append(resp, v.ResponseForDetailCustomer(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

// Customer End <-----

// -----> Cleaner Start

func (r *repository) cleanerGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		cleaners []entities.Cleaner
		resp     []entities.ResponseForDetail
		count    int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Cleaner{})

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&cleaners).Error; err != nil {
		return nil, err
	}

	for _, v := range cleaners {
		resp = append(resp, v.ResponseForDetailCleaner(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) cleanerGetByID(ctx context.Context, id uuid.UUID) (*entities.ResponseForDetail, error) {
	var cleaner entities.Cleaner

	err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", id).
		First(&cleaner).Error

	if err != nil {
		return nil, errors.New(consts.NotFoundCleaner)
	}

	resp := cleaner.ResponseForDetailCleaner(r.db)

	return &resp, nil
}

// Cleaner End <-----

func (r *repository) customerGetByID(ctx context.Context, id uuid.UUID) (*entities.ResponseForDetail, error) {
	var customer entities.Customer

	err := r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", id).
		First(&customer).Error

	if err != nil {
		return nil, errors.New(consts.NotFoundCustomer)
	}

	resp := customer.ResponseForDetailCustomer(r.db)

	return &resp, nil
}

// -----> Dashboard Stats Start

func (r *repository) getDashboardStats(ctx context.Context) (*dtos.DashboardStatsResponse, error) {
	var stats dtos.DashboardStatsResponse

	// Count total cleaners
	r.db.WithContext(ctx).Model(&entities.Cleaner{}).Count(&stats.TotalCleaner)

	// Count total customers
	r.db.WithContext(ctx).Model(&entities.Customer{}).Count(&stats.TotalCustomer)

	// Count total main categories
	r.db.WithContext(ctx).Model(&entities.ServiceCategory{}).
		Where("is_main = ?", true).
		Count(&stats.TotalMainCategory)

	// Count total sub categories
	r.db.WithContext(ctx).Model(&entities.ServiceCategory{}).
		Where("is_main = ?", false).
		Count(&stats.TotalSubCategory)

	// Count total conversations
	r.db.WithContext(ctx).Model(&entities.Conversation{}).Count(&stats.TotalConversation)

	// Count total messages
	r.db.WithContext(ctx).Model(&entities.Message{}).Count(&stats.TotalMessage)

	// Count total orders
	r.db.WithContext(ctx).Model(&entities.Order{}).Count(&stats.TotalOrder)

	// Count total offers
	r.db.WithContext(ctx).Model(&entities.Offer{}).Count(&stats.TotalOffer)

	return &stats, nil
}

// Dashboard Stats End <-----

// -----> Auth Start

func (r *repository) adminLogin(ctx context.Context, req dtos.RequestForLogin) (*dtos.AuthenticationResponse, error) {
	var admin entities.Admin

	err := r.db.WithContext(ctx).
		Model(&entities.Admin{}).
		Where("email = ?", req.Identifier).
		First(&admin).Error

	if err != nil {
		return nil, errors.New(consts.LoginFailed)
	}

	if !utils.CheckPasswordHash(req.Password, admin.Password) {
		return nil, errors.New(consts.LoginFailed)
	}

	// Generate JWT token
	cfg := config.InitConfig()
	expirationTime := time.Now().Add(time.Duration(cfg.App.JwtExpire) * time.Hour * 24)

	jwtWrapper := utils.JwtWrapper{
		SecretKey: cfg.App.JwtAdminSecret,
		Issuer:    cfg.App.JwtIssuer,
		Expire:    cfg.App.JwtExpire,
	}

	token, err := jwtWrapper.GenerateAdminJWT(ctx, admin.ID.String(), admin.Email, cfg.App.JwtAdminSecret, expirationTime)
	if err != nil {
		return nil, err
	}

	return &dtos.AuthenticationResponse{
		Token:       token,
		Expires:     expirationTime,
		IsSucceeded: true,
	}, nil
}

func (r *repository) adminLogout(ctx context.Context, adminID string) error {
	return cache.RemoveAdminSession(ctx, adminID)
}

// Auth End <-----

// -----> Conversation and Message Management Start

func (r *repository) getAllConversationsByAccount(ctx context.Context, accountID uuid.UUID, page, per_page, account_type int) (*dtos.PaginatedData, error) {
	var (
		conversations []entities.Conversation
		count         int64
	)

	base_query := r.db.WithContext(ctx).Debug().
		Model(&entities.Conversation{}).
		Where("customer_id = ? OR cleaner_id = ?", accountID, accountID)

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Preload("Customer").
		Preload("Cleaner").
		Preload("LastMessage").
		Order("last_message_at DESC NULLS LAST, created_at DESC").
		Find(&conversations).Error; err != nil {
		return nil, err
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       conversations,
	}, nil
}

func (r *repository) getMessagesByConversationID(ctx context.Context, conversationID uuid.UUID, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		messages []entities.Message
		count    int64
	)

	// Count total messages in the conversation
	countQuery := r.db.WithContext(ctx).
		Model(&entities.Message{}).
		Joins("JOIN conversations ON conversations.channel_name = messages.channel_name").
		Where("conversations.id = ?", conversationID)

	if err := countQuery.Count(&count).Error; err != nil {
		return nil, err
	}

	// Calculate offset
	offset := (page - 1) * per_page

	// Get messages with pagination
	query := r.db.WithContext(ctx).
		Joins("JOIN conversations ON conversations.channel_name = messages.channel_name").
		Where("conversations.id = ?", conversationID).
		Order("messages.created_at DESC").
		Offset(offset).
		Limit(per_page)

	if err := query.Find(&messages).Error; err != nil {
		return nil, err
	}

	// Convert to response format and get sender/receiver info
	var responses []interface{}
	for _, message := range messages {
		resp := message.MessageResponse()

		// Get sender info
		if message.SenderID != uuid.Nil {
			var senderCustomer entities.Customer
			var senderCleaner entities.Cleaner

			// Try to find sender as customer first
			if err := r.db.WithContext(ctx).Where("id = ?", message.SenderID).First(&senderCustomer).Error; err == nil {
				resp.SenderName = senderCustomer.Name + " " + senderCustomer.Surname
				resp.SenderEmail = senderCustomer.Email
				resp.SenderPhotoURL = senderCustomer.ProfilePhotoURL
				resp.SenderAccountType = 1 // Customer account type
			} else {
				// Try to find sender as cleaner
				if err := r.db.WithContext(ctx).Where("id = ?", message.SenderID).First(&senderCleaner).Error; err == nil {
					resp.SenderName = senderCleaner.Name + " " + senderCleaner.Surname
					resp.SenderEmail = senderCleaner.Email
					resp.SenderPhotoURL = senderCleaner.ProfilePhotoURL
					resp.SenderAccountType = 2 // Cleaner account type
				}
			}
		}

		// Get receiver info
		if message.ReceiverID != uuid.Nil {
			var receiverCustomer entities.Customer
			var receiverCleaner entities.Cleaner

			// Try to find receiver as customer first
			if err := r.db.WithContext(ctx).Where("id = ?", message.ReceiverID).First(&receiverCustomer).Error; err == nil {
				resp.ReceiverName = receiverCustomer.Name + " " + receiverCustomer.Surname
				resp.ReceiverEmail = receiverCustomer.Email
				resp.ReceiverPhotoURL = receiverCustomer.ProfilePhotoURL
				resp.ReceiverAccountType = 1 // Customer account type
			} else {
				// Try to find receiver as cleaner
				if err := r.db.WithContext(ctx).Where("id = ?", message.ReceiverID).First(&receiverCleaner).Error; err == nil {
					resp.ReceiverName = receiverCleaner.Name + " " + receiverCleaner.Surname
					resp.ReceiverEmail = receiverCleaner.Email
					resp.ReceiverPhotoURL = receiverCleaner.ProfilePhotoURL
					resp.ReceiverAccountType = 2 // Cleaner account type
				}
			}
		}

		responses = append(responses, resp)
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(count) / float64(per_page)))

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: totalPages,
		IsLastPage: page >= totalPages,
		Rows:       responses,
	}, nil
}

// Conversation and Message Management End <-----
