package dtos

type RequestForCreateServiceCategory struct {
	ServiceNameTR  string `json:"service_name_tr" validate:"required" example:"standart home cleaning"`
	ServiceNameEN  string `json:"service_name_en" validate:"required" example:"standart home cleaning"`
	Description    string `json:"description" validate:"required" example:"standart home cleaning description"`
	MinDuration    int    `json:"min_duration" validate:"required" example:"60"`
	IsMain         bool   `json:"is_main" example:"false"`
	MainCategoryID string `json:"main_category_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type RequestForUpdateServiceCategory struct {
	ID            string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	ServiceNameTR string `json:"service_name_tr" validate:"required" example:"standart home cleaning"`
	ServiceNameEN string `json:"service_name_en" validate:"required" example:"standart home cleaning"`
	Description   string `json:"description" validate:"required" example:"standart home cleaning description"`
	MinDuration   int    `json:"min_duration" validate:"required" example:"60"`
	IsActive      bool   `json:"is_status" validate:"required" example:"true"`
}

type RequestForDeleteServiceCategory struct {
	ID string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type RequestForOrderGetAllByID struct {
	AccountID   string `json:"account_id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType int    `json:"account_type" validate:"required" example:"1"`
	Page        int    `json:"page" example:"1"`
	PerPage     int    `json:"per_page" example:"10"`
}

type RequestForPaginate struct {
	Page        int `json:"page" example:"1"`
	PerPage     int `json:"per_page" example:"10"`
	AccountType int `json:"account_type" example:"1"`
	Approved    int `json:"approved" example:"1"`
}

type DashboardStatsResponse struct {
	TotalCleaner      int64 `json:"total_cleaner" example:"150"`
	TotalCustomer     int64 `json:"total_customer" example:"500"`
	TotalMainCategory int64 `json:"total_main_category" example:"5"`
	TotalSubCategory  int64 `json:"total_sub_category" example:"25"`
	TotalConversation int64 `json:"total_conversation" example:"300"`
	TotalMessage      int64 `json:"total_message" example:"5000"`
	TotalOrder        int64 `json:"total_order" example:"200"`
	TotalOffer        int64 `json:"total_offer" example:"800"`
}
