version: "3"
services:
  temizlik-delisi-db:
    image: "postgis/postgis:14-3.3"
    container_name:  temizlik-delisi-db
    volumes:
      -  temizlik_delisi_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "127.0.0.1:5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}

  temizlik-delisi:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image:  temizlik-delisi
    container_name:  temizlik-delisi
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
      - ./uploads:/app/uploads
    ports:
      - 8000:8000
    depends_on:
      -  temizlik-delisi-db
      -  temizlik-delisi-redis
  
  temizlik-delisi-redis:
    image: "redis:latest"
    container_name:  temizlik-delisi-redis
    networks:
      - main
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

  temizlik-delisi-centrifugo:
    image: centrifugo/centrifugo:v6
    container_name: temizlik-delisi-centrifugo
    networks:
      - main
    ports:
      - "9000:8000"
    restart: unless-stopped
    volumes:
      - ./centrifugoconfig.yaml:/centrifugo/config.yaml:ro
    command: centrifugo --config=/centrifugo/config.yaml
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

volumes:
  temizlik_delisi_data:

networks:
  main:
    name: main_network
    driver: bridge