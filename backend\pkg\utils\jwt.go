package utils

import (
	"context"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/temizlik-delisi/pkg/cache"
	"github.com/temizlik-delisi/pkg/config"
)

type JwtWrapper struct {
	SecretKey string
	Issuer    string
	Expire    int
}

type JwtCustomClaim struct {
	ID             string
	Email          string
	Timezone       string
	PhoneLanguage  string
	PushNotifToken string
	PurchaseID     string
	AccountType    int
	Os             string
	jwt.RegisteredClaims
}

type JwtAdminClaim struct {
	ID          string
	Email       string
	AccountType int
	jwt.RegisteredClaims
}

func (j *JwtWrapper) ParseToken(tokenString string) (claims *JwtCustomClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtCustomClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.<PERSON>), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtCustomClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ParseAdminToken(tokenString string) (claims *JwtAdminClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtAdminClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtAdminClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtAdminClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ValidateToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtCustomClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtCustomClaim)

	if !cache.AccountSessionCheck(claims.ID, tokenString, getAccountType(claims.AccountType)) {
		return false
	}

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) ValidateAdminToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtAdminClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtAdminClaim)

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) GenerateJWT(ctx context.Context, id, email, timezone, phone_language, push_notif_token, purchase_id, os string, account_type int) (string, error) {
	claims := &JwtCustomClaim{
		ID:             id,
		Email:          email,
		Timezone:       timezone,
		PhoneLanguage:  phone_language,
		PushNotifToken: push_notif_token,
		PurchaseID:     purchase_id,
		AccountType:    account_type,
		Os:             os,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Local().Add(time.Hour * 24 * time.Duration(config.InitConfig().App.JwtExpire))),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}

	if err := cache.SetAccountSession(ctx, id, signedToken, getAccountType(account_type), (time.Hour * 24 * 14)); err != nil {
		return "", err
	}

	return signedToken, nil
}

func (j *JwtWrapper) GenerateAdminJWT(ctx context.Context, id, email, secretKey string, expirationTime time.Time) (string, error) {
	claims := &JwtAdminClaim{
		ID:          id,
		Email:       email,
		AccountType: 3, // Admin account type
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			Issuer:    config.InitConfig().App.JwtIssuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(secretKey))
	if err != nil {
		return "", err
	}
	return signedToken, nil
}

func getAccountType(account_type int) string {
	if account_type == 1 {
		return "customer"
	}
	return "cleaner"
}
