{"error_should_bind_json": "An error was encountered, please try again later.", "error_no_data": "No data found.", "error_login": "An error occurred while logging in. Please try again.", "error_request": "the payload or id that you sent is wrong or incorrect.", "error_upload_file": "An error occurred while uploading the file.", "error_invalid_file_type": "Invalid file type. Please upload a .doc, .docx, or .pdf file.", "error_reference_processing": "An error occurred while processing the reference.", "error_unauthorized_client": "Unauthorized Client", "error_unauthorized_account_type": "Unauthorized Account Type", "error_create": "An error occurred while creating the data.", "error_update": "An error occurred while updating data.", "error_delete": "An error occurred while deleting data.", "error_process": "An error occurred during the operation.", "error_get": "There was a problem fetching data. Please try again.", "error_limit_order_count": "You have reached the limit of your order count. Please upgrade your license.", "time_error_start_date_cannot_be_less_than_x_hour_after_current_time": "Start date cannot be less than x hour after current time.", "time_error_start_date_cannot_be_before_current_time": "Start date cannot be before current time.", "time_error_end_date_cannot_be_before_start_time": "End date cannot be before start time.", "time_error_duration_cannot_be_less_than_min_duration": "Duration cannot be less than min duration.", "error-file-upload-profile-photo": "An error occurred while uploading the profile photo.", "error-file-upload-only_image_files_are_allowed": "Only image files are allowed.", "success_create": "Data created successfully.", "success_update": "Data updated successfully.", "success_delete": "Data deleted successfully.", "success_process": "The operation completed successfully.", "licence_is_not_valid": "License is not valid, please check the license.", "authorization_token_required_error": "Authorization token is required.", "authorization_incorrect_format_error": "The authorization token format is incorrect.", "authorization_token_invalid_or_expired_error": "The token has expired, please try again later.", "already_exist_comment": "You have already commented on this service.", "already_exist_service": "This service already exists.", "already_exist_customer_preference": "You have already added this customer preference.", "already_exist_file": "This file already exists.", "already_exist_language": "This language already exists.", "already_exist_order": "This order already exists.", "already_exist_cleaner": "This cleaner already exists.", "already_exist_customer": "This customer already exists.", "already_exist_address": "This address already exists.", "already_exist_offer": "This offer already exists.", "already_exist_preference": "This preference already exists.", "already_exist_service_category": "This service category already exists.", "already_exist_work_experience": "This work experience already exists.", "already_tc_verified": "TC already verified.", "already_exist_favorite": "This favorite already exists.", "reference_id_already_used_for_this_account": "This reference id already used for this account.", "not_found_order": "Order not found.", "not_found_blog": "Blog not found.", "not_found_comment": "Comment not found.", "not_found_cleaner": "Cleaner not found.", "not_found_customer": "Customer not found.", "not_found_offer": "Offer not found.", "not_found_address": "Address not found.", "not_found_customer_preference": "Customer Preference not found.", "not_found_service_category": "Service category not found.", "not_found_language": "Language not found.", "not_found_work_experience": "Work experience not found.", "not_found_account_type": "Account type not found.", "not_found_favorite": "Favorite not found.", "invalid_tc_number": "Invalid TC number.", "criminal_record_document_already_uploaded": "Criminal record document already uploaded.", "criminal_record_document_not_found": "Criminal record document not found.", "criminal_record_upload_failed": "Criminal record document upload failed.", "criminal_record_invalid_file_type": "Invalid file type. Please upload a .pdf file.", "comment_cannot_comment_yourself": "You cannot comment yourself.", "comment_cannot_comment_someone_who_has_same_account_type": "You cannot comment someone who has the same account type.", "you_cannot_create_order_with_this_license": "You cannot create an order with this license. Please upgrade your license.", "you_cannot_see_this_order_with_this_license": "You cannot see this order with this license.", "you_cannot_bid_on_your_own_order": "You cannot bid on your own order.", "you_cannot_bid_on_this_order_because_of_status": "You cannot bid on this order because of its status.", "message_marked_as_read": "Messages marked as read.", "message_conversation_access_error": "You are not authorized to access this conversation."}