package profil

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
)

type Service interface {
	UpdateProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error

	GetMyCustomerProfile(ctx context.Context) (entities.ResponseForDetail, error)
	GetMyCleanerProfile(ctx context.Context) (entities.ResponseForDetail, error)

	GetProfileByID(ctx context.Context, account_type int, id uuid.UUID) (entities.ResponseForDetail, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) UpdateProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error {
	if state.GetCurrentAccountType(ctx) == 1 {
		return s.repository.updateCustomerProfile(ctx, req)
	} else if state.GetCurrentAccountType(ctx) == 2 {
		return s.repository.updateCleanerProfile(ctx, req)
	} else {
		return errors.New(consts.NotFoundAccountType)
	}
}

func (s *service) GetMyCustomerProfile(ctx context.Context) (entities.ResponseForDetail, error) {
	return s.repository.getMyCustomerProfile(ctx)
}

func (s *service) GetMyCleanerProfile(ctx context.Context) (entities.ResponseForDetail, error) {
	return s.repository.getMyCleanerProfile(ctx)
}

func (s *service) GetProfileByID(ctx context.Context, account_type int, id uuid.UUID) (entities.ResponseForDetail, error) {
	return s.repository.getProfileByID(ctx, account_type, id)
}
