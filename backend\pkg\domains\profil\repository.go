package profil

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	updateCustomerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error
	updateCleanerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error

	getMyCustomerProfile(ctx context.Context) (entities.ResponseForDetail, error)
	getMyCleanerProfile(ctx context.Context) (entities.ResponseForDetail, error)

	getProfileByID(ctx context.Context, account_type int, id uuid.UUID) (entities.ResponseForDetail, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) updateCustomerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error {
	var (
		customer entities.Customer
	)
	tx := r.db.Begin()
	tx.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&customer)

	if customer.ID == uuid.Nil {
		tx.Rollback()
		return errors.New(consts.NotFoundCustomer)
	}

	customer.Name = req.Name
	customer.Surname = req.Surname
	customer.DateOfBirth = req.DateOfBirth
	customer.Phone = req.Phone
	customer.Country = req.Country
	customer.IsProfileUpdated = true
	if req.ProfilePhotoURL != "" {
		customer.ProfilePhotoURL = req.ProfilePhotoURL
	}

	if req.ReferenceID != "" && !customer.UsedReferenceID {
		var control_reference entities.Reference
		tx.WithContext(ctx).
			Model(&entities.Reference{}).
			Where("account_who_registered = ?", state.GetCurrentID(ctx)).
			Where("reference_id = ?", req.ReferenceID).
			First(&control_reference)

		if control_reference.ID != uuid.Nil {
			tx.Rollback()
			return errors.New(consts.ReferenceIDAlreadyUsedForThisAccount)
		}

		// -----> create reference
		control_reference.ReferenceID = req.ReferenceID
		control_reference.AccountWhoRegistered = state.GetCurrentID(ctx)

		if err := tx.WithContext(ctx).
			Model(&entities.Reference{}).
			Create(&control_reference).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
		}

		customer.UsedReferenceID = true

		// save customer
		if err := tx.WithContext(ctx).
			Model(&entities.Customer{}).
			Where("id = ?", state.GetCurrentID(ctx)).
			Save(&customer).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
		}

		// find data by reference_id in reference table
		var total_references []entities.Reference
		tx.WithContext(ctx).
			Model(&entities.Reference{}).
			Where("reference_id = ?", req.ReferenceID).
			Find(&total_references)

		if len(total_references) >= 4 {
			// get account with reference id
			var (
				customer               entities.Customer
				a_id                   uuid.UUID
				a_type                 int
				a_did_get_gift_license bool
			)
			tx.WithContext(ctx).
				Model(&entities.Customer{}).
				Where("reference_id = ?", req.ReferenceID).
				First(&customer)

			if customer.ID == uuid.Nil {
				// find cleaner
				var cleaner entities.Cleaner
				tx.WithContext(ctx).
					Model(&entities.Cleaner{}).
					Where("reference_id = ?", req.ReferenceID).
					First(&cleaner)

				if cleaner.ID == uuid.Nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, consts.NotFoundAnyAccountWithReferenceID)
				}
				a_id = cleaner.ID
				a_type = 2
				a_did_get_gift_license = cleaner.DidGetGiftLicenseCosOfReference
			} else {
				a_id = customer.ID
				a_type = 1
				a_did_get_gift_license = customer.DidGetGiftLicenseCosOfReference
			}

			// eğer almadıysa
			if !a_did_get_gift_license {
				// find last license for this account
				var last_license entities.License
				if err := tx.WithContext(ctx).
					Model(&entities.License{}).
					Where("account_id = ?", a_id).
					Where("account_type = ?", a_type).
					Order("created_at desc").
					First(&last_license).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
				}

				// create new license for this account
				var (
					new_license  entities.License
					new_end_date time.Time
				)

				switch last_license.Type {
				case 1:
					new_end_date = time.Now().AddDate(0, 1, 0)
					new_license = entities.License{
						Type:               2,
						OrderCount:         0,
						AdRate:             2,
						DoSeeEveryCategory: false,
						AccountID:          a_id,
						AccountType:        a_type,
						EndDate:            &new_end_date,
					}
				case 2:
					if last_license.EndDate != nil && last_license.EndDate.Unix() > time.Now().Unix() {
						new_end_date = last_license.EndDate.AddDate(0, 1, 0)
						last_license.EndDate = &new_end_date
					}
					new_license = entities.License{
						Type:               3,
						OrderCount:         0,
						AdRate:             3,
						DoSeeEveryCategory: true,
						AccountID:          a_id,
						AccountType:        a_type,
						EndDate:            &new_end_date,
					}
				case 3:
					if last_license.EndDate != nil && last_license.EndDate.Unix() > time.Now().Unix() {
						new_end_date = last_license.EndDate.AddDate(0, 1, 0)
						last_license.EndDate = &new_end_date
					}
					new_license = entities.License{
						Type:               3,
						OrderCount:         0,
						AdRate:             3,
						DoSeeEveryCategory: true,
						AccountID:          a_id,
						AccountType:        a_type,
						EndDate:            &new_end_date,
					}
				}

				if err := tx.WithContext(ctx).
					Model(&entities.License{}).
					Create(&new_license).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
				}
			}

			// update did_get_gift_license_cos_of_reference to true
			switch a_type {
			case 1:
				if err := tx.WithContext(ctx).
					Model(&entities.Customer{}).
					Where("id = ?", a_id).
					Update("did_get_gift_license_cos_of_reference", true).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
				}
			case 2:
				if err := tx.WithContext(ctx).
					Model(&entities.Cleaner{}).
					Where("id = ?", a_id).
					Update("did_get_gift_license_cos_of_reference", true).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
				}
			}
			// send notification to account
		}
	}

	if err := tx.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Save(&customer).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) updateCleanerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error {
	var (
		cleaner entities.Cleaner
	)
	tx := r.db.Begin()
	tx.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&cleaner)

	if cleaner.ID == uuid.Nil {
		tx.Rollback()
		return errors.New(consts.NotFoundCleaner)
	}

	cleaner.Title = req.Title
	cleaner.Description = req.Description
	cleaner.Name = req.Name
	cleaner.Surname = req.Surname
	cleaner.DateOfBirth = req.DateOfBirth
	cleaner.Phone = req.Phone
	cleaner.Country = req.Country
	cleaner.IsProfileUpdated = true
	if req.ProfilePhotoURL != "" {
		cleaner.ProfilePhotoURL = req.ProfilePhotoURL
	}
	cleaner.AvailableDates = req.AvailableDates

	if req.ReferenceID != "" && !cleaner.UsedReferenceID {
		var control_reference entities.Reference
		tx.WithContext(ctx).
			Model(&entities.Reference{}).
			Where("account_who_registered = ?", state.GetCurrentID(ctx)).
			Where("reference_id = ?", req.ReferenceID).
			First(&control_reference)

		if control_reference.ID != uuid.Nil {
			tx.Rollback()
			return errors.New(consts.ReferenceIDAlreadyUsedForThisAccount)
		}

		// -----> create reference
		control_reference.ReferenceID = req.ReferenceID
		control_reference.AccountWhoRegistered = state.GetCurrentID(ctx)

		if err := tx.WithContext(ctx).
			Model(&entities.Reference{}).
			Create(&control_reference).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
		}

		cleaner.UsedReferenceID = true

		// save cleaner
		if err := tx.WithContext(ctx).
			Model(&entities.Cleaner{}).
			Where("id = ?", state.GetCurrentID(ctx)).
			Save(&cleaner).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
		}

		var total_references []entities.Reference
		tx.WithContext(ctx).
			Model(&entities.Reference{}).
			Where("reference_id = ?", req.ReferenceID).
			Find(&total_references)

		if len(total_references) >= 4 {
			// get account with reference id
			var (
				customer               entities.Customer
				cleaner                entities.Cleaner
				a_id                   uuid.UUID
				a_type                 int
				a_did_get_gift_license bool
			)
			tx.WithContext(ctx).
				Model(&entities.Customer{}).
				Where("reference_id = ?", req.ReferenceID).
				First(&customer)

			if customer.ID == uuid.Nil {
				// find cleaner
				tx.WithContext(ctx).
					Model(&entities.Cleaner{}).
					Where("reference_id = ?", req.ReferenceID).
					First(&cleaner)

				if cleaner.ID == uuid.Nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, consts.NotFoundAnyAccountWithReferenceID)
				}
				a_id = cleaner.ID
				a_type = 2
				a_did_get_gift_license = cleaner.DidGetGiftLicenseCosOfReference
			} else {
				a_id = customer.ID
				a_type = 1
				a_did_get_gift_license = customer.DidGetGiftLicenseCosOfReference
			}

			// eğer almadıysa

			if !a_did_get_gift_license {
				// find last license for this account
				var last_license entities.License
				if err := tx.WithContext(ctx).
					Model(&entities.License{}).
					Where("account_id = ?", a_id).
					Where("account_type = ?", a_type).
					Order("created_at desc").
					First(&last_license).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
				}

				// create new license for this account
				var (
					new_license  entities.License
					new_end_date time.Time
				)

				switch last_license.Type {
				case 1:
					new_end_date = time.Now().AddDate(0, 1, 0)
					new_license = entities.License{
						Type:               2,
						OrderCount:         0,
						AdRate:             2,
						DoSeeEveryCategory: false,
						AccountID:          a_id,
						AccountType:        a_type,
						EndDate:            &new_end_date,
					}
				case 2:
					if last_license.EndDate != nil && last_license.EndDate.Unix() > time.Now().Unix() {
						new_end_date = last_license.EndDate.AddDate(0, 1, 0)
						last_license.EndDate = &new_end_date
					}
					new_license = entities.License{
						Type:               3,
						OrderCount:         0,
						AdRate:             3,
						DoSeeEveryCategory: true,
						AccountID:          a_id,
						AccountType:        a_type,
						EndDate:            &new_end_date,
					}
				case 3:
					if last_license.EndDate != nil && last_license.EndDate.Unix() > time.Now().Unix() {
						new_end_date = last_license.EndDate.AddDate(0, 1, 0)
						last_license.EndDate = &new_end_date
					}
					new_license = entities.License{
						Type:               3,
						OrderCount:         0,
						AdRate:             3,
						DoSeeEveryCategory: true,
						AccountID:          a_id,
						AccountType:        a_type,
						EndDate:            &new_end_date,
					}
				}
				if err := tx.WithContext(ctx).
					Model(&entities.License{}).
					Create(&new_license).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
				}

				// update did_get_gift_license_cos_of_reference to true
				switch a_type {
				case 1:
					if err := tx.WithContext(ctx).
						Model(&entities.Customer{}).
						Where("id = ?", a_id).
						Update("did_get_gift_license_cos_of_reference", true).Error; err != nil {
						tx.Rollback()
						return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
					}
				case 2:
					if err := tx.WithContext(ctx).
						Model(&entities.Cleaner{}).
						Where("id = ?", a_id).
						Update("did_get_gift_license_cos_of_reference", true).Error; err != nil {
						tx.Rollback()
						return fmt.Errorf(consts.ErrorReferenceProcessing, err.Error())
					}
				}

			}
			// send notification to account
		}
	}

	if err := tx.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Save(&cleaner).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) getMyCustomerProfile(ctx context.Context) (entities.ResponseForDetail, error) {
	var (
		customer entities.Customer
		resp     entities.ResponseForDetail
	)
	err := r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&customer).Error

	resp = customer.ResponseForDetailCustomer(r.db)

	return resp, err
}

func (r *repository) getMyCleanerProfile(ctx context.Context) (entities.ResponseForDetail, error) {
	var (
		cleaner entities.Cleaner
		resp    entities.ResponseForDetail
	)
	err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&cleaner).Error

	resp = cleaner.ResponseForDetailCleaner(r.db)

	return resp, err
}

func (r *repository) getProfileByID(ctx context.Context, account_type int, id uuid.UUID) (entities.ResponseForDetail, error) {
	var (
		customer entities.Customer
		cleaner  entities.Cleaner
		resp     entities.ResponseForDetail
	)

	switch account_type {
	case 1:
		err := r.db.WithContext(ctx).
			Model(&entities.Customer{}).
			Where("id = ?", id).
			First(&customer).Error
		if err != nil {
			return entities.ResponseForDetail{}, err
		}
		resp = customer.ResponseForDetailCustomer(r.db)
		resp.GoogleID = nil
		resp.AppleID = nil
		resp.PurchaseID = ""
		resp.PushNotifToken = ""
	case 2:
		err := r.db.WithContext(ctx).
			Model(&entities.Cleaner{}).
			Where("id = ?", id).
			First(&cleaner).Error
		if err != nil {
			return entities.ResponseForDetail{}, err
		}
		resp = cleaner.ResponseForDetailCleaner(r.db)
		resp.GoogleID = nil
		resp.AppleID = nil
		resp.PurchaseID = ""
		resp.PushNotifToken = ""
	default:
		return entities.ResponseForDetail{}, errors.New(consts.NotFoundAccountType)
	}

	return resp, nil
}
