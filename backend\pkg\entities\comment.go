package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Comment struct {
	Base

	Commenter              uuid.UUID `json:"commenter" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	WhoReceivedComment     uuid.UUID `json:"who_received_comment" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	WhoReceivedAccountType int       `json:"who_received_account_type" example:"1"`
	JustRate               bool      `json:"just_rate" gorm:"default:false" example:"false"`
	Rating                 int       `json:"rating" example:"5"`
	Comment                string    `json:"comment" example:"very good"`
	ApprovedByAdmin        bool      `json:"approved_by_admin" gorm:"default:false" example:"false"`
}

type CommentResponse struct {
	ID                 string       `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt          string       `json:"created_at" example:"2021-01-01 00:00:00"`
	Commenter          string       `json:"commenter" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	WhoReceivedComment UserResponse `json:"who_received_comment"`
	JustRate           bool         `json:"just_rate" gorm:"default:false" example:"false"`
	Rating             int          `json:"rating" example:"5"`
	Comment            string       `json:"comment" example:"very good"`
	ApprovedByAdmin    bool         `json:"approved_by_admin" gorm:"default:false" example:"false"`
}

func (c *Comment) Response(db *gorm.DB) CommentResponse {
	var resp CommentResponse

	resp.ID = c.ID.String()
	resp.CreatedAt = c.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Commenter = c.Commenter.String()

	if c.WhoReceivedAccountType == 1 {
		var customer Customer
		db.Model(&Customer{}).
			Where("id = ?", c.WhoReceivedComment).
			First(&customer)

		resp.WhoReceivedComment = UserResponse{
			AccountID:        customer.ID.String(),
			AccountType:      "customer",
			Name:             customer.Name,
			Surname:          customer.Surname,
			Email:            customer.Email,
			ProfilePhotoURL:  customer.ProfilePhotoURL,
			IsProfileUpdated: customer.IsProfileUpdated,
			TotalPoint:       customer.TotalPoint,
			TotalComment:     customer.TotalComment,
			AverageRate:      customer.AverageRate,
		}
	} else {
		var cleaner Cleaner
		db.Model(&Cleaner{}).
			Where("id = ?", c.WhoReceivedComment).
			First(&cleaner)

		resp.WhoReceivedComment = UserResponse{
			AccountID:        cleaner.ID.String(),
			AccountType:      "cleaner",
			Name:             cleaner.Name,
			Surname:          cleaner.Surname,
			Email:            cleaner.Email,
			ProfilePhotoURL:  cleaner.ProfilePhotoURL,
			IsProfileUpdated: cleaner.IsProfileUpdated,
			TotalPoint:       cleaner.TotalPoint,
			TotalComment:     cleaner.TotalComment,
			AverageRate:      cleaner.AverageRate,
		}
	}

	resp.JustRate = c.JustRate
	resp.Rating = c.Rating
	resp.Comment = c.Comment
	resp.ApprovedByAdmin = c.ApprovedByAdmin

	return resp
}
