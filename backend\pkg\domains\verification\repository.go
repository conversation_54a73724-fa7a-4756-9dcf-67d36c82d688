package verification

import (
	"context"
	"errors"
	"mime/multipart"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	verifyTC(ctx context.Context, tc string) error
	uploadCriminalRecordDocument(ctx context.Context, file multipart.File, filename string, fileSize int64, contentType string) error
	controlCriminalRecordDocument(ctx context.Context) error
	deleteCriminalRecordDocument(ctx context.Context) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) verifyTC(ctx context.Context, tc string) error {
	var cleaner entities.Cleaner
	r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&cleaner)

	if cleaner.ID == uuid.Nil {
		return errors.New("not_found_cleaner")
	}

	if cleaner.TCNoVerified {
		return errors.New("already_tc_verified")
	}

	if !utils.ValidateTCKimlikNo(tc) {
		return errors.New("invalid_tc_number")
	}

	cleaner.TCNo = tc
	cleaner.TCNoVerified = false

	if err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Save(&cleaner).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) uploadCriminalRecordDocument(ctx context.Context, file multipart.File, filename string, fileSize int64, contentType string) error {
	fileURL, objectName, err := utils.UploadCriminalRecordDocument(file, fileSize, contentType, filename)
	if err != nil {
		return err
	}

	criminal_record := &entities.CriminalRecordDocument{
		CleanerID: state.GetCurrentID(ctx),
		URL:       fileURL,
		Verified:  false,
	}

	tx := r.db.Begin()
	if err := tx.WithContext(ctx).
		Model(&entities.CriminalRecordDocument{}).
		Create(&criminal_record).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err = r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Updates(map[string]interface{}{
			"criminal_record_document_url":      fileURL,
			"criminal_record_document_filename": objectName,
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) deleteCriminalRecordDocument(ctx context.Context) error {
	var cleaner entities.Cleaner
	r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&cleaner)

	if cleaner.ID == uuid.Nil {
		return errors.New("not_found_cleaner")
	}

	if cleaner.CriminalRecordDocumentFilename == "" {
		return errors.New("criminal_record_document_not_found")
	}

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Updates(map[string]interface{}{
			"criminal_record_document_url":      "",
			"criminal_record_document_filename": "",
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := utils.DeleteCriminalRecordDocument(cleaner.CriminalRecordDocumentFilename); err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) controlCriminalRecordDocument(ctx context.Context) error {
	var cleaner entities.Cleaner
	r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&cleaner)

	if cleaner.ID == uuid.Nil {
		return errors.New("not_found_cleaner")
	}

	var criminal_record entities.CriminalRecordDocument
	r.db.WithContext(ctx).
		Model(&entities.CriminalRecordDocument{}).
		Where("cleaner_id = ?", state.GetCurrentID(ctx)).
		First(&criminal_record)

	if criminal_record.ID != uuid.Nil {
		return errors.New("criminal_record_document_already_uploaded")
	}

	return nil
}
