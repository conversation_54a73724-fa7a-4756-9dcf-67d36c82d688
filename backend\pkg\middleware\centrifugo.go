package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/utils"
)

// CentrifugoAuthRequest represents the authentication request from Centrifugo
type CentrifugoAuthRequest struct {
	Token string `json:"token"`
}

// CentrifugoAuthResponse represents the authentication response to Centrifugo
type CentrifugoAuthResponse struct {
	Result *CentrifugoAuthResult `json:"result,omitempty"`
	Error  *CentrifugoError      `json:"error,omitempty"`
}

type CentrifugoAuthResult struct {
	User     string                 `json:"user"`
	Channels []string               `json:"channels,omitempty"`
	Info     map[string]interface{} `json:"info,omitempty"`
}

type CentrifugoError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// CentrifugoAuth handles authentication requests from Centrifugo
func CentrifugoAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req CentrifugoAuthRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Error: &CentrifugoError{
					Code:    400,
					Message: "Invalid request format",
				},
			})
			return
		}

		// Validate the token
		centrifugo := utils.InitCentrifugo()
		claims, err := centrifugo.ValidateConnectionToken(req.Token)
		if err != nil {
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Error: &CentrifugoError{
					Code:    401,
					Message: "Invalid or expired token",
				},
			})
			return
		}

		userID, err := uuid.Parse(claims.Sub)
		if err != nil {
			c.JSON(http.StatusOK, CentrifugoAuthResponse{
				Error: &CentrifugoError{
					Code:    401,
					Message: "Invalid user ID in token",
				},
			})
			return
		}

		// Generate user's allowed channels
		channels := generateUserChannels(userID)

		// Return successful authentication
		c.JSON(http.StatusOK, CentrifugoAuthResponse{
			Result: &CentrifugoAuthResult{
				User:     userID.String(),
				Channels: channels,
				Info: map[string]interface{}{
					"user_id": userID.String(),
				},
			},
		})
	}
}

// CentrifugoSubscribe handles subscription requests from Centrifugo
func CentrifugoSubscribe() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			User    string `json:"user"`
			Channel string `json:"channel"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    400,
					"message": "Invalid request format",
				},
			})
			return
		}

		userID, err := uuid.Parse(req.User)
		if err != nil {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    401,
					"message": "Invalid user ID",
				},
			})
			return
		}

		// Check if user is allowed to subscribe to this channel
		if !isUserAllowedToSubscribe(userID, req.Channel) {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    403,
					"message": "Access denied to channel",
				},
			})
			return
		}

		// Allow subscription
		c.JSON(http.StatusOK, map[string]interface{}{
			"result": map[string]interface{}{},
		})
	}
}

// CentrifugoPublish handles publish requests from Centrifugo (optional, for additional validation)
func CentrifugoPublish() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			User    string      `json:"user"`
			Channel string      `json:"channel"`
			Data    interface{} `json:"data"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    400,
					"message": "Invalid request format",
				},
			})
			return
		}

		userID, err := uuid.Parse(req.User)
		if err != nil {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    401,
					"message": "Invalid user ID",
				},
			})
			return
		}

		// Check if user is allowed to publish to this channel
		if !isUserAllowedToPublish(userID, req.Channel) {
			c.JSON(http.StatusOK, map[string]interface{}{
				"error": map[string]interface{}{
					"code":    403,
					"message": "Access denied to publish to channel",
				},
			})
			return
		}

		// Allow publish
		c.JSON(http.StatusOK, map[string]interface{}{
			"result": map[string]interface{}{},
		})
	}
}

// generateUserChannels generates the list of channels a user is allowed to access
func generateUserChannels(userID uuid.UUID) []string {
	channels := []string{
		fmt.Sprintf("personal:%s", userID.String()), // Personal channel for notifications
	}

	// Note: Private conversation channels are dynamically allowed in isUserAllowedToSubscribe
	// We don't pre-generate them here as there could be many

	return channels
}

// isUserAllowedToSubscribe checks if a user is allowed to subscribe to a specific channel
func isUserAllowedToSubscribe(userID uuid.UUID, channel string) bool {
	// Personal channel
	personalChannel := fmt.Sprintf("personal:%s", userID.String())
	if channel == personalChannel {
		return true
	}

	// Private conversation channels
	if strings.HasPrefix(channel, "private:") {
		// Extract user IDs from channel name: private:user1:user2
		parts := strings.Split(channel, ":")
		if len(parts) == 3 {
			user1ID := parts[1]
			user2ID := parts[2]

			// User can subscribe if they are one of the participants
			if user1ID == userID.String() || user2ID == userID.String() {
				return true
			}
		}
	}

	return false
}

// isUserAllowedToPublish checks if a user is allowed to publish to a specific channel
func isUserAllowedToPublish(userID uuid.UUID, channel string) bool {
	// For now, we don't allow direct publishing from clients
	// All messages should go through our API endpoints
	// This provides better control and validation
	return false
}

// CentrifugoWebhookAuth validates Centrifugo webhook requests
func CentrifugoWebhookAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get API key from header
		apiKey := c.GetHeader("Authorization")
		if apiKey == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Missing authorization header",
			})
			return
		}

		// Remove "Bearer " prefix if present
		if strings.HasPrefix(apiKey, "Bearer ") {
			apiKey = strings.TrimPrefix(apiKey, "Bearer ")
		}

		// Validate API key
		cfg := config.InitConfig()
		if apiKey != cfg.Centrifugo.APIKey {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid API key",
			})
			return
		}

		c.Next()
	}
}
