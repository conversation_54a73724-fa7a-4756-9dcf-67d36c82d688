package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Favorite struct {
	Base

	AccountID   uuid.UUID `json:"account_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType int       `json:"account_type" example:"1"`
	OrderID     uuid.UUID `json:"order_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type FavoriteResponse struct {
	ID          string        `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt   string        `json:"created_at" example:"2021-01-01 00:00:00"`
	AccountID   string        `json:"account_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType int           `json:"account_type" example:"1"`
	OrderID     string        `json:"order_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Order       OrderResponse `json:"order"`
}

func (f *Favorite) Response(db *gorm.DB) FavoriteResponse {
	var resp FavoriteResponse

	resp.ID = f.ID.String()
	resp.CreatedAt = f.CreatedAt.Format("2006-01-02 15:04:05")
	resp.AccountID = f.AccountID.String()
	resp.AccountType = f.AccountType
	resp.OrderID = f.OrderID.String()

	var order Order
	db.Model(&Order{}).
		Where("id = ?", f.OrderID).
		First(&order)

	resp.Order = order.Response(db)

	return resp
}
