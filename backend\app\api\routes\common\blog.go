package routes

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/blog"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Get All Blog
// @Description Get All Blog
// @Tags Common-Blog
// @Security none
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Success 200 {object} dtos.PaginateResponseForSwagger
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/blog [GET]
func GetAllBlog(s blog.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.GetAllBlogs(c, page, per_page)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get All Blog Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get All Blog",
			Message:     "Blog Get All Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Blog By ID
// @Description Get Blog By ID
// @Tags Common-Blog
// @Security none
// @Produce  json
// @Param id path string true "blog id"
// @Success 200 {object} entities.BlogResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/blog/{id} [GET]
func GetBlogByID(s blog.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Blog Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		resp, err := s.GetBlogByID(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Blog Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "not_found_blog" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("not_found_blog", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Blog",
			Message:     "Blog Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
