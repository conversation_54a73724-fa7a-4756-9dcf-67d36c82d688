package dtos

// GetResponseStatusNotFound is the response for not found error
type GetResponseStatusNotFound struct {
	Error  string `json:"error" example:"Not Found"`
	Status int    `json:"status" example:"404"`
}

// GetResponseStatusBadRequest is the response for bad request error
type GetResponseStatusBadRequest struct {
	Error  string `json:"error" example:"Bad Request"`
	Status int    `json:"status" example:"400"`
}

// GetResponseStatusInternalServerError is the response for internal server error
type GetResponseStatusInternalServerError struct {
	Error  string `json:"error" example:"Internal Server Error"`
	Status int    `json:"status" example:"500"`
}

// GetResponseStatusOK is the response for success
type GetResponseStatusOK struct {
	Data   string `json:"data" example:".. successfully fetched"`
	Status int    `json:"status" example:"200"`
}

// GetResponseStatusCreated is the response for created, updated, deleted or processed successfully
type GetResponseStatusCreated struct {
	Data   string `json:"data" example:".. created, updated, deleted or processed successfully"`
	Status int    `json:"status" example:"201"`
}

// PaginateResponseForSwagger is the response for paginate
type PaginateResponseForSwagger struct {
	Data   PaginatedData `json:"data"`
	Status int           `json:"status" example:"200"`
}
