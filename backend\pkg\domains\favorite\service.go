package favorite

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
)

type Service interface {
	FavoriteCreate(ctx context.Context, req dtos.RequestForCreateFavorite) error
	FavoriteDelete(ctx context.Context, id uuid.UUID) error
	FavoriteGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) FavoriteCreate(ctx context.Context, req dtos.RequestForCreateFavorite) error {
	return s.repository.favoriteCreate(ctx, req)
}

func (s *service) FavoriteDelete(ctx context.Context, id uuid.UUID) error {
	return s.repository.favoriteDelete(ctx, id)
}

func (s *service) FavoriteGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.favoriteGetAll(ctx, page, per_page)
}
