package entities

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Message struct {
	Base
	SenderID    uuid.UUID  `json:"sender_id" gorm:"type:uuid;not null;index"`
	ReceiverID  uuid.UUID  `json:"receiver_id" gorm:"type:uuid;not null;index"`
	Content     string     `json:"content" gorm:"type:text;not null"`
	MessageType string     `json:"message_type" gorm:"default:'text'"` // text, image, file
	FileURL     string     `json:"file_url,omitempty"`
	FileName    string     `json:"file_name,omitempty"`
	IsRead      bool       `json:"is_read" gorm:"default:false"`
	ReadAt      *time.Time `json:"read_at,omitempty"`
	ChannelName string     `json:"channel_name" gorm:"index"` // private channel name for Centrifugo

	// Relations - Note: We use interface{} here because sender/receiver can be either Customer or Cleaner
	// The actual relationship will be determined by the account type in the business logic
}

type MessageResponse struct {
	ID          uuid.UUID  `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	SenderID    uuid.UUID  `json:"sender_id" example:"123e4567-e89b-12d3-a456-************"`
	ReceiverID  uuid.UUID  `json:"receiver_id" example:"123e4567-e89b-12d3-a456-************"`
	Content     string     `json:"content" example:"Hello, how are you?"`
	MessageType string     `json:"message_type" example:"text"`
	FileURL     string     `json:"file_url,omitempty" example:"https://example.com/file.jpg"`
	FileName    string     `json:"file_name,omitempty" example:"image.jpg"`
	IsRead      bool       `json:"is_read" example:"false"`
	ReadAt      *time.Time `json:"read_at,omitempty" example:"2023-01-01T12:00:00Z"`
	ChannelName string     `json:"channel_name" example:"private:customer-id:cleaner-id"`
	CreatedAt   time.Time  `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt   time.Time  `json:"updated_at" example:"2023-01-01T12:00:00Z"`

	// Sender info
	SenderName     string `json:"sender_name,omitempty" example:"John Doe"`
	SenderEmail    string `json:"sender_email,omitempty" example:"<EMAIL>"`
	SenderPhotoURL string `json:"sender_photo_url,omitempty" example:"https://example.com/photo.jpg"`

	// Receiver info
	ReceiverName     string `json:"receiver_name,omitempty" example:"Jane Smith"`
	ReceiverEmail    string `json:"receiver_email,omitempty" example:"<EMAIL>"`
	ReceiverPhotoURL string `json:"receiver_photo_url,omitempty" example:"https://example.com/receiver_photo.jpg"`
}

func (m *Message) MessageResponse() MessageResponse {
	var resp MessageResponse

	resp.ID = m.ID
	resp.SenderID = m.SenderID
	resp.ReceiverID = m.ReceiverID
	resp.Content = m.Content
	resp.MessageType = m.MessageType
	resp.FileURL = m.FileURL
	resp.FileName = m.FileName
	resp.IsRead = m.IsRead
	resp.ReadAt = m.ReadAt
	resp.ChannelName = m.ChannelName
	resp.CreatedAt = m.CreatedAt
	resp.UpdatedAt = m.UpdatedAt

	return resp
}

// Conversation represents a conversation between two users
type Conversation struct {
	Base
	CustomerID    uuid.UUID  `json:"customer_id" gorm:"type:uuid;not null;index"`
	CleanerID     uuid.UUID  `json:"cleaner_id" gorm:"type:uuid;not null;index"`
	ChannelName   string     `json:"channel_name" gorm:"uniqueIndex"` // unique channel name
	LastMessageID *uuid.UUID `json:"last_message_id,omitempty" gorm:"type:uuid"`
	LastMessageAt *time.Time `json:"last_message_at,omitempty"`

	// Relations
	Customer    *Customer `json:"customer,omitempty" gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE"`
	Cleaner     *Cleaner  `json:"cleaner,omitempty" gorm:"foreignKey:CleanerID;constraint:OnDelete:CASCADE"`
	LastMessage *Message  `json:"last_message,omitempty" gorm:"foreignKey:LastMessageID;constraint:OnDelete:SET NULL"`
}

type ConversationResponse struct {
	ID            uuid.UUID        `json:"id" example:"123e4567-e89b-12d3-a456-************"`
	CustomerID    uuid.UUID        `json:"customer_id" example:"123e4567-e89b-12d3-a456-************"`
	CleanerID     uuid.UUID        `json:"cleaner_id" example:"123e4567-e89b-12d3-a456-************"`
	ChannelName   string           `json:"channel_name" example:"private:customer-id:cleaner-id"`
	LastMessage   *MessageResponse `json:"last_message,omitempty"`
	LastMessageAt *time.Time       `json:"last_message_at,omitempty" example:"2023-01-01T12:00:00Z"`
	UnreadCount   int              `json:"unread_count" example:"3"`
	CreatedAt     time.Time        `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt     time.Time        `json:"updated_at" example:"2023-01-01T12:00:00Z"`

	// Other participant info
	ParticipantName     string `json:"participant_name" example:"Jane Smith"`
	ParticipantEmail    string `json:"participant_email" example:"<EMAIL>"`
	ParticipantPhotoURL string `json:"participant_photo_url,omitempty" example:"https://example.com/photo.jpg"`
}

func (c *Conversation) ConversationResponse(db *gorm.DB, ctx context.Context) ConversationResponse {
	var resp ConversationResponse

	resp.ID = c.ID
	resp.CustomerID = c.CustomerID
	resp.CleanerID = c.CleanerID
	resp.ChannelName = c.ChannelName
	resp.LastMessageAt = c.LastMessageAt
	resp.CreatedAt = c.CreatedAt
	resp.UpdatedAt = c.UpdatedAt

	// find unread count
	var count int64
	db.Model(&Message{}).
		Where("channel_name = ? AND receiver_id = ? AND is_read = false", c.ChannelName, state.GetCurrentID(ctx)).
		Count(&count)

	resp.UnreadCount = int(count)

	return resp
}

// GenerateChannelName creates a unique channel name for private conversation
func GenerateChannelName(customerID, cleanerID uuid.UUID) string {
	// Sort IDs to ensure consistent channel names regardless of who initiates
	if customerID.String() < cleanerID.String() {
		return "private:" + customerID.String() + ":" + cleanerID.String()
	}
	return "private:" + cleanerID.String() + ":" + customerID.String()
}
