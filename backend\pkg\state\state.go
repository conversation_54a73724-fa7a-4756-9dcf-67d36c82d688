package state

import (
	"context"

	"github.com/google/uuid"
)

const (
	CurrentUserID         = "CurrentID"
	CurrentEmail          = "CurrentEmail"
	CurrentTimezone       = "CurrentTimezone"
	CurrentPhoneLanguage  = "CurrentPhoneLanguage"
	CurrentPushNotifToken = "CurrentPushNotifToken"
	CurrentPurchaseID     = "CurrentPurchaseID"
	CurrentUserIP         = "CurrentUserIP"
	CurrentAccountType    = "CurrentAccountType"
	CurrentOS             = "CurrentOS"

	// Admin specific states
	CurrentAdminID    = "CurrentAdminID"
	CurrentAdminEmail = "CurrentAdminEmail"
)

func GetCurrentID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentUserID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func GetCurrentEmail(ctx context.Context) string {
	value := ctx.Value(CurrentEmail)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentTimezone(ctx context.Context) string {
	value := ctx.Value(CurrentTimezone)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentPhoneLanguage(ctx context.Context) string {
	value := ctx.Value(CurrentPhoneLanguage)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentPushNotifToken(ctx context.Context) string {
	value := ctx.Value(CurrentPushNotifToken)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentPurchaseID(ctx context.Context) string {
	value := ctx.Value(CurrentPurchaseID)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentUserIP(ctx context.Context) string {
	value := ctx.Value(CurrentUserIP)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentAccountType(ctx context.Context) int {
	value := ctx.Value(CurrentAccountType)
	if value == nil {
		return 0
	}
	return value.(int)
}

func GetCurrentOS(ctx context.Context) string {
	value := ctx.Value(CurrentOS)
	if value == nil {
		return ""
	}
	return value.(string)
}

// Admin specific getters
func GetCurrentAdminID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentAdminID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func GetCurrentAdminEmail(ctx context.Context) string {
	value := ctx.Value(CurrentAdminEmail)
	if value == nil {
		return ""
	}
	return value.(string)
}
