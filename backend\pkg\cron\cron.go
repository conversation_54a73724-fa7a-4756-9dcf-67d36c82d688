package cron

import (
	"context"
	"log"

	"github.com/go-co-op/gocron/v2"
	"github.com/temizlik-delisi/pkg/database"
	"github.com/temizlik-delisi/pkg/domains/order"
)

func MyCron() {
	s, _ := gocron.NewScheduler()

	orderStatusJob, _ := s.<PERSON>(
		gocron.CronJob(
			`*/5 * * * *`, // Her 5 dakikada bir
			true,
		),
		gocron.NewTask(
			func() {
				ctx := context.Background()
				db := database.DBClient()

				orderRepo := order.NewRepo(db)
				orderService := order.NewService(orderRepo)

				if err := orderService.UpdateOrderStatusByCron(ctx); err != nil {
					log.Printf("Order status update cron failed: %v", err)
				} else {
					log.Println("Order status update cron completed successfully")
				}
			},
		),
	)

	log.Printf("Order status update cron job created with ID: %s", orderStatusJob.ID())
	s.Start()
	log.Println("MyCron started...")
}
