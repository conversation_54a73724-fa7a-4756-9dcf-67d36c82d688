http_server:
  port: "8000"

log:
  level: "info"

client:
  allowed_origins:
    - "http://localhost:8080"
    - "http://127.0.0.1:8080"
    - "http://localhost:8000"
    - "http://127.0.0.1:8000"

  token:
    hmac_secret_key: "kGs6Hlz0xc5sAYy9fG1FyInWc4ThCi"

http_api:
  key: "isIMTito5ixPDa36Rtpuh4vro9hDRM"

admin:
  enabled: true
  password: "QOd9624Ag88j"
  secret: "admin-temizlik-delisi-secret-key"

engine:
  type: "memory"

# Kanal ayarları
channel:
  # Namespace olmayan kanallar için ayarlar
  without_namespace:
    # Kanaldaki mesaj geçmişi
    history_size: 150
    # Geçmiş mesajların tutulma süresi (saniye cinsinden)
    history_ttl: "600s"
    # İstemcilerin bu kanala direkt yayın yapmasına izin ver
    allow_publish_for_client: true
    # İstemcilerin abone olmasına izin ver
    allow_subscribe_for_client: true

  namespaces:
    - name: "private"
      history_size: 100
      history_ttl: "300s"
      allow_publish_for_client: true
      allow_subscribe_for_client: true

    - name: "personal"
      history_size: 100
      history_ttl: "300s"
      allow_publish_for_client: true
      allow_subscribe_for_client: true

websocket:
  read_buffer_size: 4096