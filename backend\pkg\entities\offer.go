package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Offer struct {
	Base

	OrderID           uuid.UUID `json:"order_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderID          uuid.UUID `json:"bidder_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderAccountType int       `json:"bidder_account_type" example:"1"`
	Description       string    `json:"description" example:"I can clean your house"`
	NewStartDate      string    `json:"new_start_date" example:"2021-01-01 00:00:00"`
	NewEndDate        string    `json:"new_end_date" example:"2021-01-01 00:00:00"`
	NewPrice          float64   `json:"new_price" example:"100"`
	IsAccepted        bool      `json:"is_accepted" gorm:"default:false" example:"false"`
}

type OfferResponse struct {
	ID                string  `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt         string  `json:"created_at" example:"2021-01-01 00:00:00"`
	OrderID           string  `json:"order_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderID          string  `json:"bidder_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderAccountType int     `json:"bidder_account_type" example:"1"`
	Description       string  `json:"description" example:"I can clean your house"`
	NewStartDate      string  `json:"new_start_date" example:"2021-01-01 00:00:00"`
	NewEndDate        string  `json:"new_end_date" example:"2021-01-01 00:00:00"`
	NewPrice          float64 `json:"new_price" example:"100"`

	BidderName            string `json:"bidder_name" example:"John"`
	BidderSurname         string `json:"bidder_surname" example:"Doe"`
	BidderEmail           string `json:"bidder_email" example:"<EMAIL>"`
	BidderProfilePhotoURL string `json:"bidder_profile_photo_url" example:"https://minio.example.com/profile-photos/photo.jpg"`

	OrderTitle            string  `json:"order_title" example:"Clean my house"`
	OrderDescription      string  `json:"order_description" example:"I need someone to clean my house"`
	OrderStartDate        string  `json:"order_start_date" example:"2021-01-01 00:00:00"`
	OrderEndDate          string  `json:"order_end_date" example:"2021-01-01 00:00:00"`
	OrderPricePerCleaner  float64 `json:"order_price_per_cleaner" example:"100"`
	OrderTotalPrice       float64 `json:"order_total_price" example:"100"`
	OrderHowManyCleaners  int     `json:"order_how_many_cleaners" example:"1"`
	OrderHouseSize        int     `json:"order_house_size" example:"120"`
	OrderRoomNumbers      string  `json:"order_room_numbers" example:"3+1"`
	OrderStatus           int     `json:"order_status" example:"1"`
	OrderCategoryNameTR   string  `json:"order_category_name_tr" example:"Cleaning"`
	OrderCategoryNameEN   string  `json:"order_category_name_en" example:"Cleaning"`
	OrderCategoryImageURL string  `json:"order_category_image_url" example:"https://minio.example.com/category-images/cleaning.jpg"`
}

func (o *Offer) Response(db *gorm.DB) OfferResponse {
	var resp OfferResponse

	resp.ID = o.ID.String()
	resp.CreatedAt = o.CreatedAt.Format("2006-01-02 15:04:05")
	resp.OrderID = o.OrderID.String()
	resp.BidderID = o.BidderID.String()
	resp.BidderAccountType = o.BidderAccountType
	resp.Description = o.Description
	resp.NewStartDate = o.NewStartDate
	resp.NewEndDate = o.NewEndDate
	resp.NewPrice = o.NewPrice

	// add order info by order_id
	var order Order
	db.Model(&Order{}).
		Where("id = ?", o.OrderID).
		First(&order)

	resp.OrderTitle = order.Title
	resp.OrderDescription = order.Description
	resp.OrderStartDate = order.StartDate.Format("2006-01-02 15:04:05")
	resp.OrderEndDate = order.EndDate.Format("2006-01-02 15:04:05")
	resp.OrderPricePerCleaner = order.PricePerCleaner
	resp.OrderTotalPrice = order.TotalPrice
	resp.OrderHowManyCleaners = order.HowManyCleaners
	resp.OrderHouseSize = order.HouseSize
	resp.OrderRoomNumbers = order.RoomNumbers
	resp.OrderStatus = order.Status

	var service_category ServiceCategory
	db.Model(&ServiceCategory{}).
		Where("id = ?", order.ServiceCategoryID).
		First(&service_category)

	resp.OrderCategoryNameTR = service_category.ServiceNameTR
	resp.OrderCategoryNameEN = service_category.ServiceNameEN
	resp.OrderCategoryImageURL = service_category.ImageURL

	// add bidder info by bidder_account_type and bidder_id
	if o.BidderAccountType == 1 {
		var customer Customer
		db.Model(&Customer{}).
			Where("id = ?", o.BidderID).
			First(&customer)

		resp.BidderName = customer.Name
		resp.BidderSurname = customer.Surname
		resp.BidderEmail = customer.Email
		resp.BidderProfilePhotoURL = customer.ProfilePhotoURL
	} else {
		var cleaner Cleaner
		db.Model(&Cleaner{}).
			Where("id = ?", o.BidderID).
			First(&cleaner)

		resp.BidderName = cleaner.Name
		resp.BidderSurname = cleaner.Surname
		resp.BidderEmail = cleaner.Email
		resp.BidderProfilePhotoURL = cleaner.ProfilePhotoURL
	}

	return resp
}
