package dtos

import "time"

type RequestForLogin struct {
	Identifier string `json:"identifier" validate:"required" example:"<EMAIL>"`
	Password   string `json:"password" validate:"required" example:"123456"`
}

type RequestForRegister struct {
	Name                   string `json:"name" validate:"required" example:"<PERSON>"`
	Surname                string `json:"surname" validate:"required" example:"<PERSON><PERSON>"`
	Email                  string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password               string `json:"password" validate:"required" example:"123456"`
	UserType               int    `json:"user_type" validate:"required" example:"1"` // 1. Normal User 2. Cleaner
	TimeZone               string `json:"time_zone" validate:"required" example:"Europe/Istanbul"`
	PhoneLanguage          string `json:"phone_language" validate:"required" example:"tr"`
	Os                     string `json:"os" validate:"required" example:"android"`
	PurchaseID             string `json:"purchase_id" validate:"required" example:"**********"`
	PushNotifToken         string `json:"push_notif_token" validate:"required" example:"**********"`
	LastVersionName        string `json:"last_version_name" example:"1.0.0"`
	LastVersionBuildNumber int    `json:"last_version_build_number" example:"3"`
}

type CreateUserRespDto struct {
	Message string `json:"message"`
}

type AuthenticationResponse struct {
	Token       string    `json:"Token"`
	Expires     time.Time `json:"Expires"`
	IsSucceeded bool      `json:"IsSucceeded"`
}

type AuthenticationResponseForSwagger struct {
	Data   AuthenticationResponse `json:"data"`
	Status int                    `json:"status" example:"200"`
}

type RequestForGoogleLogin struct {
	GoogleIDToken          string `json:"google_id_token" validate:"required" example:"ya29.a0AfH6SMC..."`
	AccountType            int    `json:"account_type" validate:"required" example:"1"` // 1. Client 2. Cleaner
	TimeZone               string `json:"time_zone" validate:"required" example:"Europe/Istanbul"`
	PhoneLanguage          string `json:"phone_language" validate:"required" example:"tr"`
	Os                     string `json:"os" validate:"required" example:"android"`
	PurchaseID             string `json:"purchase_id" validate:"required" example:"**********"`
	PushNotifToken         string `json:"push_notif_token" validate:"required" example:"**********"`
	LastVersionName        string `json:"last_version_name" example:"1.0.0"`
	LastVersionBuildNumber int    `json:"last_version_build_number" example:"3"`
}

type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
}

type RequestForAppleLogin struct {
	AppleCode              string `json:"apple_code" validate:"required" example:"c123456789.0.abcd.efgh"`
	AccountType            int    `json:"account_type" validate:"required" example:"1"` // 1. Normal User 2. Cleaner
	TimeZone               string `json:"time_zone" validate:"required" example:"Europe/Istanbul"`
	PhoneLanguage          string `json:"phone_language" validate:"required" example:"tr"`
	Os                     string `json:"os" validate:"required" example:"ios"`
	PurchaseID             string `json:"purchase_id" validate:"required" example:"**********"`
	PushNotifToken         string `json:"push_notif_token" validate:"required" example:"**********"`
	LastVersionName        string `json:"last_version_name" example:"1.0.0"`
	LastVersionBuildNumber int    `json:"last_version_build_number" example:"3"`
}

type AppleUserInfo struct {
	ID            string `json:"sub"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"email_verified"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
}
