package favorite

import (
	"context"
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	favoriteCreate(ctx context.Context, req dtos.RequestForCreateFavorite) error
	favoriteDelete(ctx context.Context, id uuid.UUID) error
	favoriteGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) favoriteCreate(ctx context.Context, req dtos.RequestForCreateFavorite) error {
	var favorite_control entities.Favorite
	r.db.WithContext(ctx).
		Model(&entities.Favorite{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx)).
		Where("order_id = ?", req.OrderID).
		First(&favorite_control)

	if favorite_control.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistFavorite)
	}

	favorite := &entities.Favorite{
		AccountID:   state.GetCurrentID(ctx),
		AccountType: state.GetCurrentAccountType(ctx),
		OrderID:     uuid.MustParse(req.OrderID),
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Favorite{}).
		Create(favorite).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) favoriteDelete(ctx context.Context, id uuid.UUID) error {

	var favorite entities.Favorite
	r.db.WithContext(ctx).
		Model(&entities.Favorite{}).
		Where("id = ?", id).
		First(&favorite)

	if favorite.ID == uuid.Nil {
		return errors.New(consts.NotFoundFavorite)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Favorite{}).
		Delete(&favorite).Error

	return err
}

func (r *repository) favoriteGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		favorites []entities.Favorite
		resp      []entities.FavoriteResponse
		count     int64
	)
	base_query := r.db.WithContext(ctx).
		Model(&entities.Favorite{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx))

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&favorites).Error; err != nil {

		return nil, err
	}

	for _, v := range favorites {
		resp = append(resp, v.Response(r.db))
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}
