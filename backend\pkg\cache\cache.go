package cache

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/temizlik-delisi/pkg/config"
)

var (
	client      *redis.Client
	client_once sync.Once
)

func InitRedis(rds config.Redis) {
	client_once.Do(func() {
		rdc := redis.NewClient(&redis.Options{
			Addr:     rds.Host + ":" + rds.Port,
			Password: rds.Pass,
			DB:       0,
		})
		// -----> control
		var ctx = context.Background()
		_, err := rdc.Ping(ctx).Result()
		if err != nil {
			log.Panic("Error connecting to Redis: ", err.Error())
		}
		client = rdc
	})
}

func RedisClient() *redis.Client {
	if client == nil {
		log.Panic("Redis client is not initialized. Call InitRedis first.")
	}
	return client
}

func AccountSessionCheck(id string, token, account_type string) bool {
	val, err := client.Get(context.Background(), fmt.Sprintf("temizllik-delisi-%s-auth-token-%v", account_type, id)).Result()
	if err != nil {
		return false
	}
	return val == token
}

func SetAccountSession(ctx context.Context, id, token, account_type string, ex_time time.Duration) error {
	return client.Set(ctx, fmt.Sprintf("temizllik-delisi-%s-auth-token-%v", account_type, id), token, ex_time).Err()
}

func Set(ctx context.Context, key string, value interface{}, ex_time time.Duration) error {
	return client.Set(ctx, key, value, ex_time).Err()
}

func RemoveAccountSession(ctx context.Context, id, account_type string) error {
	return client.Del(ctx, fmt.Sprintf("temizllik-delisi-%s-auth-token-%v", account_type, id)).Err()
}

// Admin session management
func AdminSessionCheck(id string, token string) bool {
	val, err := client.Get(context.Background(), fmt.Sprintf("temizllik-delisi-admin-auth-token-%v", id)).Result()
	if err != nil {
		return false
	}
	return val == token
}

func SetAdminSession(ctx context.Context, id, token string, ex_time time.Duration) error {
	return client.Set(ctx, fmt.Sprintf("temizllik-delisi-admin-auth-token-%v", id), token, ex_time).Err()
}

func RemoveAdminSession(ctx context.Context, id string) error {
	return client.Del(ctx, fmt.Sprintf("temizllik-delisi-admin-auth-token-%v", id)).Err()
}

func SetAdvertisement(ctx context.Context, id string, ex_time time.Duration) error {
	random_uuid := uuid.New().String()
	return client.Set(ctx, fmt.Sprintf("temizllik-delisi-user-advertisement-%v-%v", id, random_uuid), "", ex_time).Err()
}

func CountKeysByPattern(ctx context.Context, pattern string) (int, error) {
	var (
		cursor     uint64
		total_keys int
		for_secure int = 100
	)

	for {
		keys, newCursor, err := client.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return 0, err
		}

		total_keys += len(keys)
		cursor = newCursor

		if cursor == 0 || for_secure == 0 {
			break
		}
		for_secure--
	}

	return total_keys, nil
}
