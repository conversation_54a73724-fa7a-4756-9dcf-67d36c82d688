package message

import (
	"context"
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	// Message operations
	CreateMessage(ctx context.Context, message *entities.Message) error
	GetMessageByID(ctx context.Context, messageID uuid.UUID) (*entities.Message, error)
	GetMessagesByConversation(ctx context.Context, conversation_id uuid.UUID, page, per_page int) ([]*entities.Message, int64, error)
	MarkMessagesAsRead(ctx context.Context, conversationID, userID uuid.UUID) error
	GetUnreadMessageCount(ctx context.Context, conversationID, userID uuid.UUID) (int64, error)

	// User info operations
	GetUserInfo(ctx context.Context, userID uuid.UUID, accountType int) (name, email, photoURL string, err error)

	// Conversation operations
	CreateConversation(ctx context.Context, conversation *entities.Conversation) error
	DeleteConversation(ctx context.Context, conversationID uuid.UUID) error
	GetConversationByID(ctx context.Context, conversationID uuid.UUID) (*entities.Conversation, error)
	GetConversationByParticipants(ctx context.Context, customerID, cleanerID uuid.UUID) (*entities.Conversation, error)
	GetUserConversations(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	GetUserConversationsWithoutPagination(ctx context.Context) ([]*entities.Conversation, error)
	UpdateConversationLastMessage(ctx context.Context, conversationID, messageID uuid.UUID) error

	// Helper methods
	IsUserInConversation(ctx context.Context, conversationID, userID uuid.UUID, accountType int) (bool, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// CreateMessage creates a new message
func (r *repository) CreateMessage(ctx context.Context, message *entities.Message) error {
	return r.db.WithContext(ctx).Create(message).Error
}

// GetMessageByID retrieves a message by ID
func (r *repository) GetMessageByID(ctx context.Context, messageID uuid.UUID) (*entities.Message, error) {
	var message entities.Message
	err := r.db.WithContext(ctx).First(&message, "id = ?", messageID).Error

	if err != nil {
		return nil, err
	}

	return &message, nil
}

// GetMessagesByConversation retrieves messages for a conversation with pagination
func (r *repository) GetMessagesByConversation(ctx context.Context, conversation_id uuid.UUID, page, per_page int) ([]*entities.Message, int64, error) {
	var (
		messages     []*entities.Message
		total        int64
		conversation entities.Conversation
	)

	r.db.WithContext(ctx).
		First(&conversation, "id = ?", conversation_id)

	if conversation.ID == uuid.Nil {
		return nil, 0, errors.New(consts.NotFoundConversation)
	}

	offset := (page - 1) * per_page

	// Count total messages
	r.db.WithContext(ctx).Model(&entities.Message{}).
		Where("channel_name = ?", conversation.ChannelName).
		Count(&total)

	// Get messages with pagination
	err := r.db.WithContext(ctx).
		Where("channel_name = ?", conversation.ChannelName).
		Order("created_at DESC").
		Offset(offset).
		Limit(per_page).
		Find(&messages).Error

	return messages, total, err
}

// MarkMessagesAsRead marks all unread messages in a conversation as read for a specific user
func (r *repository) MarkMessagesAsRead(ctx context.Context, conversationID, userID uuid.UUID) error {
	var conversation entities.Conversation
	r.db.WithContext(ctx).
		First(&conversation, "id = ?", conversationID)

	if conversation.ID == uuid.Nil {
		return errors.New(consts.NotFoundConversation)
	}

	return r.db.WithContext(ctx).
		Model(&entities.Message{}).
		Where("channel_name = ? AND receiver_id = ? AND is_read = false", conversation.ChannelName, userID).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": "NOW()",
		}).Error
}

// GetUnreadMessageCount gets the count of unread messages for a user in a conversation
func (r *repository) GetUnreadMessageCount(ctx context.Context, conversationID, userID uuid.UUID) (int64, error) {
	var count int64

	// Get conversation to find channel name
	var conversation entities.Conversation
	if err := r.db.WithContext(ctx).First(&conversation, "id = ?", conversationID).Error; err != nil {
		return 0, err
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Message{}).
		Where("channel_name = ? AND receiver_id = ? AND is_read = false", conversation.ChannelName, userID).
		Count(&count).Error

	return count, err
}

func (r *repository) CreateConversation(ctx context.Context, conversation *entities.Conversation) error {
	return r.db.WithContext(ctx).Create(conversation).Error
}

func (r *repository) DeleteConversation(ctx context.Context, conversationID uuid.UUID) error {
	var conversation entities.Conversation
	r.db.WithContext(ctx).
		Where("id = ?", conversationID).
		First(&conversation)

	if conversation.ID == uuid.Nil {
		return errors.New(consts.NotFoundConversation)
	}

	tx := r.db.Begin()
	if err := tx.WithContext(ctx).
		Where("channel_name = ?", conversation.ChannelName).
		Delete(&entities.Message{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.WithContext(ctx).
		Where("id = ?", conversationID).
		Delete(&entities.Conversation{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

// GetConversationByID retrieves a conversation by ID
func (r *repository) GetConversationByID(ctx context.Context, conversationID uuid.UUID) (*entities.Conversation, error) {
	var conversation entities.Conversation
	err := r.db.WithContext(ctx).
		Preload("Customer").
		Preload("Cleaner").
		Preload("LastMessage").
		First(&conversation, "id = ?", conversationID).Error

	if err != nil {
		return nil, err
	}

	return &conversation, nil
}

// GetConversationByParticipants finds a conversation between two specific users
func (r *repository) GetConversationByParticipants(ctx context.Context, customerID, cleanerID uuid.UUID) (*entities.Conversation, error) {
	var conversation entities.Conversation
	err := r.db.WithContext(ctx).
		Preload("Customer").
		Preload("Cleaner").
		Preload("LastMessage").
		Where("customer_id = ? AND cleaner_id = ?", customerID, cleanerID).
		First(&conversation).Error

	if err != nil {
		return nil, err
	}

	return &conversation, nil
}

// GetUserConversations retrieves all conversations for a user with pagination
func (r *repository) GetUserConversations(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		conversations []*entities.Conversation
		count         int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Conversation{})

	// Filter based on account type
	if state.GetCurrentAccountType(ctx) == 1 { // Customer
		base_query = base_query.Where("customer_id = ?", state.GetCurrentID(ctx))
	} else { // Cleaner
		base_query = base_query.Where("cleaner_id = ?", state.GetCurrentID(ctx))
	}

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Preload("Customer").
		Preload("Cleaner").
		Preload("LastMessage").
		Order("last_message_at DESC NULLS LAST, created_at DESC").
		Find(&conversations).Error; err != nil {
		return nil, err
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       conversations,
	}, nil
}

// GetUserConversationsWithoutPagination retrieves all conversations for a user without pagination
func (r *repository) GetUserConversationsWithoutPagination(ctx context.Context) ([]*entities.Conversation, error) {
	var conversations []*entities.Conversation

	base_query := r.db.WithContext(ctx).
		Model(&entities.Conversation{})

	if state.GetCurrentAccountType(ctx) == 1 {
		base_query = base_query.Where("customer_id = ?", state.GetCurrentID(ctx))
	} else {
		base_query = base_query.Where("cleaner_id = ?", state.GetCurrentID(ctx))
	}

	if err := base_query.
		Order("created_at DESC").
		Find(&conversations).Error; err != nil {
		return nil, err
	}

	return conversations, nil
}

// UpdateConversationLastMessage updates the last message info for a conversation
func (r *repository) UpdateConversationLastMessage(ctx context.Context, conversationID, messageID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&entities.Conversation{}).
		Where("id = ?", conversationID).
		Updates(map[string]interface{}{
			"last_message_id": messageID,
			"last_message_at": "NOW()",
		}).Error
}

// IsUserInConversation checks if a user is part of a conversation
func (r *repository) IsUserInConversation(ctx context.Context, conversationID, userID uuid.UUID, accountType int) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&entities.Conversation{}).Where("id = ?", conversationID)

	if accountType == 1 { // Customer
		query = query.Where("customer_id = ?", userID)
	} else { // Cleaner
		query = query.Where("cleaner_id = ?", userID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// GetUserInfo retrieves user information based on account type
func (r *repository) GetUserInfo(ctx context.Context, userID uuid.UUID, accountType int) (name, email, photoURL string, err error) {
	if accountType == 1 { // Customer
		var customer entities.Customer
		err = r.db.WithContext(ctx).Select("name, surname, email, profile_photo_url").First(&customer, "id = ?", userID).Error
		if err != nil {
			return "", "", "", err
		}
		return customer.Name + " " + customer.Surname, customer.Email, customer.ProfilePhotoURL, nil
	} else { // Cleaner
		var cleaner entities.Cleaner
		err = r.db.WithContext(ctx).Select("name, surname, email, profile_photo_url").First(&cleaner, "id = ?", userID).Error
		if err != nil {
			return "", "", "", err
		}
		return cleaner.Name + " " + cleaner.Surname, cleaner.Email, cleaner.ProfilePhotoURL, nil
	}
}
